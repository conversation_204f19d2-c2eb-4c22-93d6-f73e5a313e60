<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الأرباح والخسائر</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }
        
        .report-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
        }
        
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #198754;
        }
        
        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #198754;
            margin-bottom: 10px;
        }
        
        .report-period {
            font-size: 1.2rem;
            color: #6c757d;
            margin: 0;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        
        .company-name {
            font-size: 1.5rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 5px;
        }
        
        .financial-statement {
            margin-bottom: 30px;
        }
        
        .statement-section {
            margin-bottom: 25px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .section-header {
            background-color: #198754;
            color: white;
            padding: 15px;
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }
        
        .section-content {
            padding: 0;
        }
        
        .statement-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }
        
        .statement-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .statement-table tr:last-child td {
            border-bottom: none;
        }
        
        .statement-table .item-label {
            font-weight: 500;
            color: #495057;
        }
        
        .statement-table .item-value {
            text-align: left;
            font-weight: 600;
            color: #212529;
        }
        
        .statement-table .subtotal-row {
            background-color: #f8f9fa;
            border-top: 2px solid #dee2e6;
        }
        
        .statement-table .subtotal-row td {
            font-weight: 700;
            color: #198754;
        }
        
        .statement-table .total-row {
            background-color: #198754;
            color: white;
        }
        
        .statement-table .total-row td {
            font-weight: 700;
            font-size: 1.1rem;
        }
        
        .profit-positive {
            color: #198754;
        }
        
        .profit-negative {
            color: #dc3545;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-box {
            text-align: center;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #198754;
        }
        
        .footer-section {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 0;
                font-size: 12px;
            }
            
            .report-container {
                max-width: none;
                margin: 0;
                padding: 15mm;
            }
            
            .report-header {
                page-break-inside: avoid;
            }
            
            .statement-section {
                page-break-inside: avoid;
            }
            
            .footer-section {
                page-break-inside: avoid;
            }
            
            @page {
                margin: 15mm;
                size: A4;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- رأس التقرير -->
        <div class="report-header">
            <h1 class="report-title">تقرير الأرباح والخسائر</h1>
            <p class="report-period">
                من {{ start_date|date:"d/m/Y" }} إلى {{ end_date|date:"d/m/Y" }}
            </p>
        </div>
        
        <!-- معلومات الشركة -->
        <div class="company-info">
            <h2 class="company-name">{{ company.company_name }}</h2>
            {% if company.company_name_en %}
                <p class="text-muted mb-1">{{ company.company_name_en }}</p>
            {% endif %}
            <p class="mb-1">{{ company.address }}</p>
            <p class="mb-0">هاتف: {{ company.phone }} | بريد إلكتروني: {{ company.email }}</p>
        </div>
        
        <!-- الإحصائيات الملخصة -->
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-label">إجمالي الإيرادات</div>
                <div class="stat-value">{{ total_sales|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">إجمالي المصروفات</div>
                <div class="stat-value">{{ total_purchases|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">صافي الربح/الخسارة</div>
                <div class="stat-value {% if gross_profit >= 0 %}profit-positive{% else %}profit-negative{% endif %}">
                    {{ gross_profit|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}
                </div>
            </div>
        </div>
        
        <!-- قائمة الأرباح والخسائر -->
        <div class="financial-statement">
            <!-- الإيرادات -->
            <div class="statement-section">
                <h3 class="section-header">الإيرادات</h3>
                <div class="section-content">
                    <table class="statement-table">
                        <tr>
                            <td class="item-label">إيرادات المبيعات</td>
                            <td class="item-value">{{ total_sales|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                        </tr>
                        <tr class="subtotal-row">
                            <td class="item-label">إجمالي الإيرادات</td>
                            <td class="item-value">{{ total_sales|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- المصروفات -->
            <div class="statement-section">
                <h3 class="section-header">المصروفات</h3>
                <div class="section-content">
                    <table class="statement-table">
                        <tr>
                            <td class="item-label">تكلفة المشتريات</td>
                            <td class="item-value">{{ total_purchases|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                        </tr>
                        <tr class="subtotal-row">
                            <td class="item-label">إجمالي المصروفات</td>
                            <td class="item-value">{{ total_purchases|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- النتيجة النهائية -->
            <div class="statement-section">
                <h3 class="section-header">النتيجة النهائية</h3>
                <div class="section-content">
                    <table class="statement-table">
                        <tr>
                            <td class="item-label">إجمالي الإيرادات</td>
                            <td class="item-value">{{ total_sales|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                        </tr>
                        <tr>
                            <td class="item-label">ناقص: إجمالي المصروفات</td>
                            <td class="item-value">({{ total_purchases|floatformat:2|default:0 }}) {{ base_currency_symbol|default:"د.ع" }}</td>
                        </tr>
                        <tr class="total-row">
                            <td class="item-label">
                                {% if gross_profit >= 0 %}
                                    صافي الربح
                                {% else %}
                                    صافي الخسارة
                                {% endif %}
                            </td>
                            <td class="item-value">{{ gross_profit|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- نسب الربحية -->
        <div class="statement-section">
            <h3 class="section-header">نسب الربحية</h3>
            <div class="section-content">
                <table class="statement-table">
                    <tr>
                        <td class="item-label">هامش الربح الإجمالي</td>
                        <td class="item-value">{{ profit_margin|floatformat:2|default:0 }}%</td>
                    </tr>
                    <tr>
                        <td class="item-label">نسبة المصروفات إلى الإيرادات</td>
                        <td class="item-value">
                            {% if total_sales > 0 %}
                                {% widthratio total_purchases total_sales 100 %}%
                            {% else %}
                                0.00%
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- ملاحظات -->
        <div style="margin-bottom: 30px;">
            <h3 style="font-size: 1.3rem; font-weight: 600; color: #198754; margin: 30px 0 15px 0; padding-bottom: 8px; border-bottom: 2px solid #198754;">ملاحظات</h3>
            <div style="padding: 15px; background-color: #f8f9fa; border-radius: 8px; border-right: 4px solid #198754;">
                <ul style="margin: 0; padding-right: 20px;">
                    <li><strong>الإيرادات:</strong> تشمل جميع إيرادات المبيعات خلال الفترة المحددة</li>
                    <li><strong>المصروفات:</strong> تشمل تكلفة المشتريات والمصروفات التشغيلية</li>
                    <li><strong>هامش الربح:</strong> يحسب كنسبة مئوية من صافي الربح إلى إجمالي الإيرادات</li>
                    <li><strong>هذا التقرير:</strong> يعكس الأداء المالي للشركة خلال الفترة المحددة</li>
                </ul>
            </div>
        </div>
        
        <!-- تذييل التقرير -->
        <div class="footer-section">
            <p class="mb-1">تم إنشاء هذا التقرير في {{ current_date|date:"d/m/Y H:i" }}</p>
            <p class="mb-0">{{ company.company_name }} - نظام الفواتير العراقي</p>
        </div>
    </div>
    
    <!-- أزرار الطباعة -->
    <div class="no-print" style="text-align: center; margin: 30px 0;">
        <button onclick="window.print()" style="background: #198754; color: white; border: none; padding: 12px 24px; border-radius: 5px; font-size: 16px; margin: 0 10px; cursor: pointer;">
            طباعة التقرير
        </button>
        <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 5px; font-size: 16px; margin: 0 10px; cursor: pointer;">
            إغلاق
        </button>
    </div>
</body>
</html>
