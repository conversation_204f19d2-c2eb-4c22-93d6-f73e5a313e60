<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير أرصدة الموردين</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }
        
        .report-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
        }
        
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #fd7e14;
        }
        
        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #fd7e14;
            margin-bottom: 10px;
        }
        
        .report-date {
            font-size: 1.2rem;
            color: #6c757d;
            margin: 0;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        
        .company-name {
            font-size: 1.5rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 5px;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-box {
            text-align: center;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #fd7e14;
        }
        
        .stat-value.positive {
            color: #198754;
        }
        
        .stat-value.negative {
            color: #dc3545;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #fd7e14;
            margin: 30px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #fd7e14;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .data-table th {
            background-color: #fd7e14;
            color: white;
            font-weight: 600;
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #e55a00;
            font-size: 0.9rem;
        }
        
        .data-table td {
            padding: 10px 8px;
            border: 1px solid #dee2e6;
            text-align: center;
            font-size: 0.85rem;
        }
        
        .data-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .data-table tfoot th {
            background-color: #e9ecef;
            color: #212529;
            font-weight: 700;
        }
        
        .text-end {
            text-align: left !important;
        }
        
        .text-right {
            text-align: right !important;
        }
        
        .balance-positive {
            font-weight: 600;
            color: #198754;
        }
        
        .balance-negative {
            font-weight: 600;
            color: #dc3545;
        }
        
        .balance-zero {
            font-weight: 600;
            color: #6c757d;
        }
        
        .footer-section {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 0;
                font-size: 12px;
            }
            
            .report-container {
                max-width: none;
                margin: 0;
                padding: 15mm;
            }
            
            .report-header {
                page-break-inside: avoid;
            }
            
            .summary-stats {
                page-break-inside: avoid;
            }
            
            .data-table {
                page-break-inside: auto;
            }
            
            .data-table tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
            
            .section-title {
                page-break-after: avoid;
            }
            
            .footer-section {
                page-break-inside: avoid;
            }
            
            @page {
                margin: 15mm;
                size: A4;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- رأس التقرير -->
        <div class="report-header">
            <h1 class="report-title">تقرير أرصدة الموردين</h1>
            <p class="report-date">
                كما في {{ as_of_date|date:"d/m/Y" }}
            </p>
        </div>
        
        <!-- معلومات الشركة -->
        <div class="company-info">
            <h2 class="company-name">{{ company.company_name }}</h2>
            {% if company.company_name_en %}
                <p class="text-muted mb-1">{{ company.company_name_en }}</p>
            {% endif %}
            <p class="mb-1">{{ company.address }}</p>
            <p class="mb-0">هاتف: {{ company.phone }} | بريد إلكتروني: {{ company.email }}</p>
        </div>
        
        <!-- الإحصائيات الملخصة -->
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-label">إجمالي الموردين</div>
                <div class="stat-value">{{ total_suppliers|default:0 }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">إجمالي المستحق للموردين</div>
                <div class="stat-value negative">{{ total_payable|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">إجمالي المدفوع مقدماً</div>
                <div class="stat-value positive">{{ total_prepaid|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</div>
            </div>
        </div>
        
        <!-- تفاصيل أرصدة الموردين -->
        <h2 class="section-title">تفاصيل أرصدة الموردين</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 5%">#</th>
                    <th style="width: 25%">اسم المورد</th>
                    <th style="width: 15%">رقم الهاتف</th>
                    <th style="width: 20%">البريد الإلكتروني</th>
                    <th style="width: 15%">إجمالي المشتريات</th>
                    <th style="width: 15%">إجمالي المدفوعات</th>
                    <th style="width: 15%">الرصيد</th>
                </tr>
            </thead>
            <tbody>
                {% for supplier in supplier_balances %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td class="text-right">{{ supplier.name }}</td>
                    <td>{{ supplier.phone|default:"-" }}</td>
                    <td>{{ supplier.email|default:"-" }}</td>
                    <td class="text-end">{{ supplier.total_purchases|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                    <td class="text-end">{{ supplier.total_payments|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                    <td class="text-end 
                        {% if supplier.balance > 0 %}balance-positive
                        {% elif supplier.balance < 0 %}balance-negative
                        {% else %}balance-zero{% endif %}">
                        {{ supplier.balance|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" class="text-center">لا توجد بيانات موردين</td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr>
                    <th colspan="4" class="text-end">الإجمالي:</th>
                    <th class="text-end">{{ total_purchases|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</th>
                    <th class="text-end">{{ total_payments|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</th>
                    <th class="text-end 
                        {% if net_balance > 0 %}balance-positive
                        {% elif net_balance < 0 %}balance-negative
                        {% else %}balance-zero{% endif %}">
                        {{ net_balance|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}
                    </th>
                </tr>
            </tfoot>
        </table>
        
        <!-- ملاحظات -->
        <div style="margin-bottom: 30px;">
            <h3 class="section-title">ملاحظات</h3>
            <div style="padding: 15px; background-color: #f8f9fa; border-radius: 8px; border-right: 4px solid #fd7e14;">
                <ul style="margin: 0; padding-right: 20px;">
                    <li><strong>الرصيد الموجب:</strong> يعني أن المورد مدين لنا (دفعنا له مقدماً)</li>
                    <li><strong>الرصيد السالب:</strong> يعني أننا مدينون للمورد (مستحق الدفع)</li>
                    <li><strong>الرصيد صفر:</strong> يعني أن الحساب متوازن</li>
                </ul>
            </div>
        </div>
        
        <!-- تذييل التقرير -->
        <div class="footer-section">
            <p class="mb-1">تم إنشاء هذا التقرير في {{ current_date|date:"d/m/Y H:i" }}</p>
            <p class="mb-0">{{ company.company_name }} - نظام الفواتير العراقي</p>
        </div>
    </div>
    
    <!-- أزرار الطباعة -->
    <div class="no-print" style="text-align: center; margin: 30px 0;">
        <button onclick="window.print()" style="background: #fd7e14; color: white; border: none; padding: 12px 24px; border-radius: 5px; font-size: 16px; margin: 0 10px; cursor: pointer;">
            طباعة التقرير
        </button>
        <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 5px; font-size: 16px; margin: 0 10px; cursor: pointer;">
            إغلاق
        </button>
    </div>
</body>
</html>
