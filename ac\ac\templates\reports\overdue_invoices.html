{% extends 'base/base.html' %}

{% block title %}تقرير الفواتير المتأخرة - النظام المحاسبي{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">
                <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                تقرير الفواتير المتأخرة
            </h1>
            <div>
                <a href="{% url 'reports:overdue_invoices_print' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                   class="btn btn-danger" target="_blank">
                    <i class="fas fa-print me-2"></i>طباعة التقرير
                </a>
                <a href="{% url 'reports:index' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر التقرير -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>فلاتر التقرير
                </h5>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="as_of_date" class="form-label">كما في تاريخ</label>
                        <input type="date" class="form-control" id="as_of_date" name="as_of_date" 
                               value="{{ as_of_date|date:'Y-m-d' }}">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>عرض التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- ملخص التقرير -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h5 class="card-title">فواتير البيع المتأخرة</h5>
                <h3>{{ total_overdue_sales|floatformat:2 }} {{ base_currency_symbol }}</h3>
                <small>{{ overdue_sales|length }} فاتورة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h5 class="card-title">فواتير الشراء المتأخرة</h5>
                <h3>{{ total_overdue_purchases|floatformat:2 }} {{ base_currency_symbol }}</h3>
                <small>{{ overdue_purchases|length }} فاتورة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-dark text-white">
            <div class="card-body text-center">
                <h5 class="card-title">إجمالي المتأخرات</h5>
                <h3>{{ total_overdue|floatformat:2 }} {{ base_currency_symbol }}</h3>
                <small>{{ overdue_sales|length|add:overdue_purchases|length }} فاتورة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h5 class="card-title">كما في تاريخ</h5>
                <h3>{{ as_of_date|date:'d/m/Y' }}</h3>
                <small>تاريخ التقرير</small>
            </div>
        </div>
    </div>
</div>

<!-- فواتير البيع المتأخرة -->
{% if overdue_sales %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-file-invoice-dollar me-2"></i>
                    فواتير البيع المتأخرة ({{ overdue_sales|length }})
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>تاريخ الفاتورة</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>أيام التأخير</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in overdue_sales %}
                            <tr>
                                <td>
                                    <a href="{% url 'invoices:sales_detail' invoice.pk %}" class="text-decoration-none">
                                        {{ invoice.invoice_number }}
                                    </a>
                                </td>
                                <td>{{ invoice.customer.name }}</td>
                                <td>{{ invoice.invoice_date|date:'d/m/Y' }}</td>
                                <td>{{ invoice.due_date|date:'d/m/Y' }}</td>
                                <td>
                                    <span class="badge bg-danger">
                                        {{ invoice.due_date|timesince:as_of_date }}
                                    </span>
                                </td>
                                <td class="text-end">{{ invoice.total_amount|floatformat:2 }} {{ base_currency_symbol }}</td>
                                <td>
                                    {% if invoice.status == 'pending' %}
                                        <span class="badge bg-warning">معلقة</span>
                                    {% elif invoice.status == 'partial' %}
                                        <span class="badge bg-info">مدفوعة جزئياً</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- فواتير الشراء المتأخرة -->
{% if overdue_purchases %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">
                    <i class="fas fa-file-invoice me-2"></i>
                    فواتير الشراء المتأخرة ({{ overdue_purchases|length }})
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>المورد</th>
                                <th>تاريخ الفاتورة</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>أيام التأخير</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in overdue_purchases %}
                            <tr>
                                <td>
                                    <a href="{% url 'invoices:purchase_detail' invoice.pk %}" class="text-decoration-none">
                                        {{ invoice.invoice_number }}
                                    </a>
                                </td>
                                <td>{{ invoice.supplier.name }}</td>
                                <td>{{ invoice.invoice_date|date:'d/m/Y' }}</td>
                                <td>{{ invoice.due_date|date:'d/m/Y' }}</td>
                                <td>
                                    <span class="badge bg-warning">
                                        {{ invoice.due_date|timesince:as_of_date }}
                                    </span>
                                </td>
                                <td class="text-end">{{ invoice.total_amount|floatformat:2 }} {{ base_currency_symbol }}</td>
                                <td>
                                    {% if invoice.status == 'pending' %}
                                        <span class="badge bg-warning">معلقة</span>
                                    {% elif invoice.status == 'partial' %}
                                        <span class="badge bg-info">مدفوعة جزئياً</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- رسالة في حالة عدم وجود فواتير متأخرة -->
{% if not overdue_sales and not overdue_purchases %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-success text-center">
            <i class="fas fa-check-circle fa-3x mb-3"></i>
            <h4>ممتاز! لا توجد فواتير متأخرة</h4>
            <p>جميع الفواتير مدفوعة أو لم تتجاوز تاريخ الاستحقاق بعد.</p>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}
