<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الضرائب المفصل</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }
        
        .report-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
        }
        
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #6f42c1;
        }
        
        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #6f42c1;
            margin-bottom: 10px;
        }
        
        .report-period {
            font-size: 1.2rem;
            color: #6c757d;
            margin: 0;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        
        .company-name {
            font-size: 1.5rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 5px;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-box {
            text-align: center;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #6f42c1;
        }
        
        .stat-value.positive {
            color: #198754;
        }
        
        .stat-value.negative {
            color: #dc3545;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #6f42c1;
            margin: 30px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #6f42c1;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .data-table th {
            background-color: #6f42c1;
            color: white;
            font-weight: 600;
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #5a2d91;
            font-size: 0.9rem;
        }
        
        .data-table td {
            padding: 10px 8px;
            border: 1px solid #dee2e6;
            text-align: center;
            font-size: 0.85rem;
        }
        
        .data-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .data-table tfoot th {
            background-color: #e9ecef;
            color: #212529;
            font-weight: 700;
        }
        
        .text-end {
            text-align: left !important;
        }
        
        .text-right {
            text-align: right !important;
        }
        
        .tax-collected {
            font-weight: 600;
            color: #198754;
        }
        
        .tax-paid {
            font-weight: 600;
            color: #dc3545;
        }
        
        .tax-net {
            font-weight: 600;
            color: #6f42c1;
        }
        
        .footer-section {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 0;
                font-size: 12px;
            }
            
            .report-container {
                max-width: none;
                margin: 0;
                padding: 15mm;
            }
            
            .report-header {
                page-break-inside: avoid;
            }
            
            .summary-stats {
                page-break-inside: avoid;
            }
            
            .data-table {
                page-break-inside: auto;
            }
            
            .data-table tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
            
            .section-title {
                page-break-after: avoid;
            }
            
            .footer-section {
                page-break-inside: avoid;
            }
            
            @page {
                margin: 15mm;
                size: A4;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- رأس التقرير -->
        <div class="report-header">
            <h1 class="report-title">تقرير الضرائب المفصل</h1>
            <p class="report-period">
                من {{ start_date|date:"d/m/Y" }} إلى {{ end_date|date:"d/m/Y" }}
            </p>
        </div>
        
        <!-- معلومات الشركة -->
        <div class="company-info">
            <h2 class="company-name">{{ company.company_name }}</h2>
            {% if company.company_name_en %}
                <p class="text-muted mb-1">{{ company.company_name_en }}</p>
            {% endif %}
            <p class="mb-1">{{ company.address }}</p>
            <p class="mb-1">هاتف: {{ company.phone }} | بريد إلكتروني: {{ company.email }}</p>
            {% if company.tax_number %}
                <p class="mb-0"><strong>الرقم الضريبي:</strong> {{ company.tax_number }}</p>
            {% endif %}
        </div>
        
        <!-- الإحصائيات الملخصة -->
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-label">إجمالي الضرائب المحصلة</div>
                <div class="stat-value positive">{{ total_tax_collected|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">إجمالي الضرائب المدفوعة</div>
                <div class="stat-value negative">{{ total_tax_paid|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">صافي الضريبة المستحقة</div>
                <div class="stat-value {% if net_tax_due >= 0 %}positive{% else %}negative{% endif %}">
                    {{ net_tax_due|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}
                </div>
            </div>
        </div>
        
        <!-- تفاصيل الضرائب حسب النوع -->
        {% if tax_details %}
        <h2 class="section-title">تفاصيل الضرائب حسب النوع</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 20%">نوع الضريبة</th>
                    <th style="width: 10%">المعدل</th>
                    <th style="width: 15%">ضرائب المبيعات</th>
                    <th style="width: 10%">عدد فواتير البيع</th>
                    <th style="width: 15%">ضرائب المشتريات</th>
                    <th style="width: 10%">عدد فواتير الشراء</th>
                    <th style="width: 20%">صافي الضريبة</th>
                </tr>
            </thead>
            <tbody>
                {% for detail in tax_details %}
                <tr>
                    <td class="text-right">{{ detail.tax_type.name }}</td>
                    <td>{{ detail.tax_type.rate }}%</td>
                    <td class="text-end tax-collected">{{ detail.sales_tax|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                    <td>{{ detail.sales_count }}</td>
                    <td class="text-end tax-paid">{{ detail.purchase_tax|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                    <td>{{ detail.purchase_count }}</td>
                    <td class="text-end tax-net 
                        {% if detail.net_tax >= 0 %}positive{% else %}negative{% endif %}">
                        {{ detail.net_tax|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr>
                    <th colspan="2" class="text-end">الإجمالي:</th>
                    <th class="text-end tax-collected">{{ total_tax_collected|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</th>
                    <th></th>
                    <th class="text-end tax-paid">{{ total_tax_paid|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</th>
                    <th></th>
                    <th class="text-end tax-net 
                        {% if net_tax_due >= 0 %}positive{% else %}negative{% endif %}">
                        {{ net_tax_due|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}
                    </th>
                </tr>
            </tfoot>
        </table>
        {% endif %}
        
        <!-- ملاحظات -->
        <div style="margin-bottom: 30px;">
            <h3 class="section-title">ملاحظات</h3>
            <div style="padding: 15px; background-color: #f8f9fa; border-radius: 8px; border-right: 4px solid #6f42c1;">
                <ul style="margin: 0; padding-right: 20px;">
                    <li><strong>الضرائب المحصلة:</strong> الضرائب المحصلة من العملاء على فواتير البيع</li>
                    <li><strong>الضرائب المدفوعة:</strong> الضرائب المدفوعة للموردين على فواتير الشراء</li>
                    <li><strong>صافي الضريبة المستحقة:</strong> الفرق بين الضرائب المحصلة والمدفوعة</li>
                    <li><strong>القيمة الموجبة:</strong> تعني ضريبة مستحقة للدولة</li>
                    <li><strong>القيمة السالبة:</strong> تعني ضريبة مستردة من الدولة</li>
                </ul>
            </div>
        </div>
        
        <!-- تذييل التقرير -->
        <div class="footer-section">
            <p class="mb-1">تم إنشاء هذا التقرير في {{ current_date|date:"d/m/Y H:i" }}</p>
            <p class="mb-0">{{ company.company_name }} - نظام الفواتير العراقي</p>
        </div>
    </div>
    
    <!-- أزرار الطباعة -->
    <div class="no-print" style="text-align: center; margin: 30px 0;">
        <button onclick="window.print()" style="background: #6f42c1; color: white; border: none; padding: 12px 24px; border-radius: 5px; font-size: 16px; margin: 0 10px; cursor: pointer;">
            طباعة التقرير
        </button>
        <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 5px; font-size: 16px; margin: 0 10px; cursor: pointer;">
            إغلاق
        </button>
    </div>
</body>
</html>
