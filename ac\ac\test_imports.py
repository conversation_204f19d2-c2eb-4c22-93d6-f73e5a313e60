#!/usr/bin/env python
"""
اختبار الاستيرادات
"""

import os
import sys

# إعداد Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'accounting_system.settings')

try:
    import django
    django.setup()
    print("✅ Django setup successful")
    
    # اختبار الاستيرادات
    from reports.models import ReportManager
    print("✅ ReportManager import successful")
    
    from reports.views import SalesSummaryReportView
    print("✅ Views import successful")
    
    print("🎉 All imports successful!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
