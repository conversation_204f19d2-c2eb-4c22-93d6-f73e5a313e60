<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الفواتير المتأخرة</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }
        
        .report-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
        }
        
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #dc3545;
        }
        
        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 10px;
        }
        
        .report-date {
            font-size: 1.2rem;
            color: #6c757d;
            margin: 0;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        
        .company-name {
            font-size: 1.5rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 5px;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-box {
            text-align: center;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #dc3545;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #dc3545;
            margin: 30px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #dc3545;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .data-table th {
            background-color: #dc3545;
            color: white;
            font-weight: 600;
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #b02a37;
            font-size: 0.9rem;
        }
        
        .data-table td {
            padding: 10px 8px;
            border: 1px solid #dee2e6;
            text-align: center;
            font-size: 0.85rem;
        }
        
        .data-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .data-table tfoot th {
            background-color: #e9ecef;
            color: #212529;
            font-weight: 700;
        }
        
        .text-end {
            text-align: left !important;
        }
        
        .text-right {
            text-align: right !important;
        }
        
        .overdue-amount {
            font-weight: 600;
            color: #dc3545;
        }
        
        .days-overdue {
            font-weight: 600;
        }
        
        .days-overdue.critical {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .days-overdue.warning {
            color: #856404;
            background-color: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .footer-section {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 0;
                font-size: 12px;
            }
            
            .report-container {
                max-width: none;
                margin: 0;
                padding: 15mm;
            }
            
            .report-header {
                page-break-inside: avoid;
            }
            
            .summary-stats {
                page-break-inside: avoid;
            }
            
            .data-table {
                page-break-inside: auto;
            }
            
            .data-table tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
            
            .section-title {
                page-break-after: avoid;
            }
            
            .footer-section {
                page-break-inside: avoid;
            }
            
            @page {
                margin: 15mm;
                size: A4;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- رأس التقرير -->
        <div class="report-header">
            <h1 class="report-title">تقرير الفواتير المتأخرة</h1>
            <p class="report-date">
                كما في {{ as_of_date|date:"d/m/Y" }}
            </p>
        </div>
        
        <!-- معلومات الشركة -->
        <div class="company-info">
            <h2 class="company-name">{{ company.company_name }}</h2>
            {% if company.company_name_en %}
                <p class="text-muted mb-1">{{ company.company_name_en }}</p>
            {% endif %}
            <p class="mb-1">{{ company.address }}</p>
            <p class="mb-0">هاتف: {{ company.phone }} | بريد إلكتروني: {{ company.email }}</p>
        </div>
        
        <!-- الإحصائيات الملخصة -->
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-label">فواتير بيع متأخرة</div>
                <div class="stat-value">{{ total_overdue_sales_count|default:0 }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">قيمة فواتير البيع المتأخرة</div>
                <div class="stat-value">{{ total_overdue_sales_amount|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">فواتير شراء متأخرة</div>
                <div class="stat-value">{{ total_overdue_purchases_count|default:0 }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">قيمة فواتير الشراء المتأخرة</div>
                <div class="stat-value">{{ total_overdue_purchases_amount|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</div>
            </div>
        </div>
        
        <!-- فواتير البيع المتأخرة -->
        {% if overdue_sales %}
        <h2 class="section-title">فواتير البيع المتأخرة</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 15%">رقم الفاتورة</th>
                    <th style="width: 20%">العميل</th>
                    <th style="width: 12%">تاريخ الفاتورة</th>
                    <th style="width: 12%">تاريخ الاستحقاق</th>
                    <th style="width: 10%">أيام التأخير</th>
                    <th style="width: 16%">المبلغ المستحق</th>
                    <th style="width: 15%">الحالة</th>
                </tr>
            </thead>
            <tbody>
                {% for invoice in overdue_sales %}
                <tr>
                    <td class="text-right">{{ invoice.invoice_number }}</td>
                    <td class="text-right">{{ invoice.customer.name }}</td>
                    <td>{{ invoice.invoice_date|date:"d/m/Y" }}</td>
                    <td>{{ invoice.due_date|date:"d/m/Y" }}</td>
                    <td>
                        {% with days_overdue=invoice.days_overdue %}
                            <span class="days-overdue {% if days_overdue > 30 %}critical{% elif days_overdue > 15 %}warning{% endif %}">
                                {{ days_overdue }} يوم
                            </span>
                        {% endwith %}
                    </td>
                    <td class="text-end overdue-amount">{{ invoice.total_amount|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                    <td>
                        {% if invoice.status == 'pending' %}
                            <span style="color: #856404;">معلقة</span>
                        {% elif invoice.status == 'partial' %}
                            <span style="color: #0f5132;">مدفوعة جزئياً</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr>
                    <th colspan="5" class="text-end">الإجمالي:</th>
                    <th class="text-end overdue-amount">{{ total_overdue_sales_amount|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</th>
                    <th></th>
                </tr>
            </tfoot>
        </table>
        {% endif %}
        
        <!-- فواتير الشراء المتأخرة -->
        {% if overdue_purchases %}
        <h2 class="section-title">فواتير الشراء المتأخرة</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 15%">رقم الفاتورة</th>
                    <th style="width: 20%">المورد</th>
                    <th style="width: 12%">تاريخ الفاتورة</th>
                    <th style="width: 12%">تاريخ الاستحقاق</th>
                    <th style="width: 10%">أيام التأخير</th>
                    <th style="width: 16%">المبلغ المستحق</th>
                    <th style="width: 15%">الحالة</th>
                </tr>
            </thead>
            <tbody>
                {% for invoice in overdue_purchases %}
                <tr>
                    <td class="text-right">{{ invoice.invoice_number }}</td>
                    <td class="text-right">{{ invoice.supplier.name }}</td>
                    <td>{{ invoice.invoice_date|date:"d/m/Y" }}</td>
                    <td>{{ invoice.due_date|date:"d/m/Y" }}</td>
                    <td>
                        {% with days_overdue=invoice.days_overdue %}
                            <span class="days-overdue {% if days_overdue > 30 %}critical{% elif days_overdue > 15 %}warning{% endif %}">
                                {{ days_overdue }} يوم
                            </span>
                        {% endwith %}
                    </td>
                    <td class="text-end overdue-amount">{{ invoice.total_amount|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                    <td>
                        {% if invoice.status == 'pending' %}
                            <span style="color: #856404;">معلقة</span>
                        {% elif invoice.status == 'partial' %}
                            <span style="color: #0f5132;">مدفوعة جزئياً</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr>
                    <th colspan="5" class="text-end">الإجمالي:</th>
                    <th class="text-end overdue-amount">{{ total_overdue_purchases_amount|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</th>
                    <th></th>
                </tr>
            </tfoot>
        </table>
        {% endif %}
        
        <!-- ملاحظات -->
        <div style="margin-bottom: 30px;">
            <h3 class="section-title">ملاحظات</h3>
            <div style="padding: 15px; background-color: #f8f9fa; border-radius: 8px; border-right: 4px solid #dc3545;">
                <ul style="margin: 0; padding-right: 20px;">
                    <li><strong>فواتير البيع المتأخرة:</strong> الفواتير التي تجاوزت تاريخ الاستحقاق ولم يتم تحصيلها</li>
                    <li><strong>فواتير الشراء المتأخرة:</strong> الفواتير التي تجاوزت تاريخ الاستحقاق ولم يتم دفعها</li>
                    <li><strong>أيام التأخير:</strong> عدد الأيام منذ تاريخ الاستحقاق</li>
                    <li><strong>التحذيرات:</strong> أصفر للتأخير أكثر من 15 يوم، أحمر لأكثر من 30 يوم</li>
                </ul>
            </div>
        </div>
        
        <!-- تذييل التقرير -->
        <div class="footer-section">
            <p class="mb-1">تم إنشاء هذا التقرير في {{ current_date|date:"d/m/Y H:i" }}</p>
            <p class="mb-0">{{ company.company_name }} - نظام الفواتير العراقي</p>
        </div>
    </div>
    
    <!-- أزرار الطباعة -->
    <div class="no-print" style="text-align: center; margin: 30px 0;">
        <button onclick="window.print()" style="background: #dc3545; color: white; border: none; padding: 12px 24px; border-radius: 5px; font-size: 16px; margin: 0 10px; cursor: pointer;">
            طباعة التقرير
        </button>
        <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 5px; font-size: 16px; margin: 0 10px; cursor: pointer;">
            إغلاق
        </button>
    </div>
</body>
</html>
