<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المصروفات</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }
        
        .report-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
        }
        
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #e74c3c;
        }
        
        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #e74c3c;
            margin-bottom: 10px;
        }
        
        .report-period {
            font-size: 1.2rem;
            color: #6c757d;
            margin: 0;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        
        .company-name {
            font-size: 1.5rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 5px;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-box {
            text-align: center;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #e74c3c;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #e74c3c;
            margin: 30px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #e74c3c;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .data-table th {
            background-color: #e74c3c;
            color: white;
            font-weight: 600;
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #c0392b;
            font-size: 0.9rem;
        }
        
        .data-table td {
            padding: 10px 8px;
            border: 1px solid #dee2e6;
            text-align: center;
            font-size: 0.85rem;
        }
        
        .data-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .data-table tfoot th {
            background-color: #e9ecef;
            color: #212529;
            font-weight: 700;
        }
        
        .text-end {
            text-align: left !important;
        }
        
        .text-right {
            text-align: right !important;
        }
        
        .expense-amount {
            font-weight: 600;
            color: #e74c3c;
        }
        
        .category-operational {
            background-color: #fff3cd;
            color: #856404;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
        }
        
        .category-administrative {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
        }
        
        .category-marketing {
            background-color: #d4edda;
            color: #155724;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
        }
        
        .footer-section {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 0;
                font-size: 12px;
            }
            
            .report-container {
                max-width: none;
                margin: 0;
                padding: 15mm;
            }
            
            .report-header {
                page-break-inside: avoid;
            }
            
            .summary-stats {
                page-break-inside: avoid;
            }
            
            .data-table {
                page-break-inside: auto;
            }
            
            .data-table tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
            
            .section-title {
                page-break-after: avoid;
            }
            
            .footer-section {
                page-break-inside: avoid;
            }
            
            @page {
                margin: 15mm;
                size: A4;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- رأس التقرير -->
        <div class="report-header">
            <h1 class="report-title">تقرير المصروفات</h1>
            <p class="report-period">
                من {{ start_date|date:"d/m/Y" }} إلى {{ end_date|date:"d/m/Y" }}
            </p>
        </div>
        
        <!-- معلومات الشركة -->
        <div class="company-info">
            <h2 class="company-name">{{ company.company_name }}</h2>
            {% if company.company_name_en %}
                <p class="text-muted mb-1">{{ company.company_name_en }}</p>
            {% endif %}
            <p class="mb-1">{{ company.address }}</p>
            <p class="mb-0">هاتف: {{ company.phone }} | بريد إلكتروني: {{ company.email }}</p>
        </div>
        
        <!-- الإحصائيات الملخصة -->
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-label">إجمالي المصروفات</div>
                <div class="stat-value">{{ total_expenses|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">عدد المصروفات</div>
                <div class="stat-value">{{ expenses_count|default:0 }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">متوسط المصروف</div>
                <div class="stat-value">{{ average_expense|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">أكبر مصروف</div>
                <div class="stat-value">{{ max_expense|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</div>
            </div>
        </div>
        
        <!-- المصروفات حسب الفئة -->
        {% if expenses_by_category %}
        <h2 class="section-title">المصروفات حسب الفئة</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 30%">فئة المصروف</th>
                    <th style="width: 20%">عدد المصروفات</th>
                    <th style="width: 25%">إجمالي المبلغ</th>
                    <th style="width: 25%">النسبة من الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {% for category in expenses_by_category %}
                <tr>
                    <td class="text-right">
                        <span class="category-{{ category.category|lower }}">{{ category.category_display }}</span>
                    </td>
                    <td>{{ category.count }}</td>
                    <td class="text-end expense-amount">{{ category.total|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                    <td class="text-center">{{ category.percentage|floatformat:1 }}%</td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr>
                    <th class="text-end">الإجمالي:</th>
                    <th>{{ expenses_count }}</th>
                    <th class="text-end expense-amount">{{ total_expenses|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</th>
                    <th>100.0%</th>
                </tr>
            </tfoot>
        </table>
        {% endif %}
        
        <!-- تفاصيل المصروفات -->
        {% if expenses %}
        <h2 class="section-title">تفاصيل المصروفات</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 12%">التاريخ</th>
                    <th style="width: 25%">الوصف</th>
                    <th style="width: 15%">الفئة</th>
                    <th style="width: 18%">المبلغ</th>
                    <th style="width: 15%">طريقة الدفع</th>
                    <th style="width: 15%">المرجع</th>
                </tr>
            </thead>
            <tbody>
                {% for expense in expenses %}
                <tr>
                    <td>{{ expense.date|date:"d/m/Y" }}</td>
                    <td class="text-right">{{ expense.description }}</td>
                    <td>
                        <span class="category-{{ expense.category|lower }}">{{ expense.get_category_display }}</span>
                    </td>
                    <td class="text-end expense-amount">{{ expense.amount|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                    <td>{{ expense.payment_method|default:"-" }}</td>
                    <td>{{ expense.reference|default:"-" }}</td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr>
                    <th colspan="3" class="text-end">الإجمالي:</th>
                    <th class="text-end expense-amount">{{ total_expenses|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</th>
                    <th colspan="2"></th>
                </tr>
            </tfoot>
        </table>
        {% endif %}
        
        <!-- ملاحظات -->
        <div style="margin-bottom: 30px;">
            <h3 class="section-title">ملاحظات</h3>
            <div style="padding: 15px; background-color: #f8f9fa; border-radius: 8px; border-right: 4px solid #e74c3c;">
                <ul style="margin: 0; padding-right: 20px;">
                    <li><strong>المصروفات التشغيلية:</strong> تشمل الرواتب والإيجارات والمرافق</li>
                    <li><strong>المصروفات الإدارية:</strong> تشمل القرطاسية والاتصالات والصيانة</li>
                    <li><strong>المصروفات التسويقية:</strong> تشمل الإعلانات والعمولات</li>
                    <li><strong>هذا التقرير:</strong> يعكس جميع المصروفات المسجلة خلال الفترة المحددة</li>
                </ul>
            </div>
        </div>
        
        <!-- تذييل التقرير -->
        <div class="footer-section">
            <p class="mb-1">تم إنشاء هذا التقرير في {{ current_date|date:"d/m/Y H:i" }}</p>
            <p class="mb-0">{{ company.company_name }} - نظام الفواتير العراقي</p>
        </div>
    </div>
    
    <!-- أزرار الطباعة -->
    <div class="no-print" style="text-align: center; margin: 30px 0;">
        <button onclick="window.print()" style="background: #e74c3c; color: white; border: none; padding: 12px 24px; border-radius: 5px; font-size: 16px; margin: 0 10px; cursor: pointer;">
            طباعة التقرير
        </button>
        <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 5px; font-size: 16px; margin: 0 10px; cursor: pointer;">
            إغلاق
        </button>
    </div>
</body>
</html>
