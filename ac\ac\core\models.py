from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal

class CompanySettings(models.Model):
    """إعدادات الشركة الأساسية"""
    company_name = models.CharField(max_length=200, verbose_name="اسم الشركة")
    company_name_en = models.CharField(max_length=200, verbose_name="اسم الشركة بالإنجليزية", blank=True)
    address = models.TextField(verbose_name="العنوان")
    phone = models.Char<PERSON>ield(max_length=20, verbose_name="رقم الهاتف")
    email = models.EmailField(verbose_name="البريد الإلكتروني")
    tax_number = models.CharField(max_length=50, verbose_name="الرقم الضريبي", blank=True)
    commercial_register = models.Cha<PERSON><PERSON><PERSON>(max_length=50, verbose_name="السجل التجاري", blank=True)
    logo = models.ImageField(upload_to='company/', verbose_name="شعار الشركة", blank=True, null=True)

    # إعدادات الضرائب
    tax_enabled = models.BooleanField(default=True, verbose_name="تفعيل الضريبة")
    default_tax_rate = models.DecimalField(
        max_digits=5, decimal_places=2, default=Decimal('15.00'),
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name="نسبة الضريبة الافتراضية (%)"
    )

    # إعدادات العملة
    base_currency = models.ForeignKey(
        'Currency', on_delete=models.PROTECT,
        null=True, blank=True, verbose_name="العملة الأساسية"
    )
    currency_position = models.CharField(
        max_length=10,
        choices=[('before', 'قبل المبلغ'), ('after', 'بعد المبلغ')],
        default='after',
        verbose_name="موضع رمز العملة"
    )

    # إعدادات البنك
    bank_name = models.CharField(max_length=200, verbose_name="اسم البنك", blank=True)
    bank_account_number = models.CharField(max_length=50, verbose_name="رقم الحساب البنكي", blank=True)
    bank_iban = models.CharField(max_length=50, verbose_name="IBAN", blank=True)
    bank_swift_code = models.CharField(max_length=20, verbose_name="SWIFT Code", blank=True)

    # إعدادات الفواتير
    default_invoice_template = models.ForeignKey(
        'InvoiceTemplate', on_delete=models.SET_NULL,
        null=True, blank=True, verbose_name="قالب الفاتورة الافتراضي"
    )
    invoice_prefix = models.CharField(max_length=10, default='INV', verbose_name="بادئة رقم الفاتورة")
    invoice_number_length = models.IntegerField(default=6, verbose_name="طول رقم الفاتورة")

    # التوقيع الرقمي
    digital_signature = models.ImageField(
        upload_to='signatures/', verbose_name="التوقيع الرقمي",
        blank=True, null=True
    )
    signature_text = models.CharField(
        max_length=200, verbose_name="نص التوقيع", blank=True
    )

    # شروط الدفع الافتراضية
    default_payment_terms = models.CharField(
        max_length=100,
        default='الدفع خلال 30 يوم',
        verbose_name="شروط الدفع الافتراضية"
    )

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "إعدادات الشركة"
        verbose_name_plural = "إعدادات الشركة"

    def __str__(self):
        return self.company_name

class BaseModel(models.Model):
    """نموذج أساسي مشترك"""
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="أنشئ بواسطة")

    class Meta:
        abstract = True

class Currency(BaseModel):
    """نموذج العملات المحسن"""
    SUPPORTED_CURRENCIES = [
        ('IQD', 'الدينار العراقي'),
        ('USD', 'الدولار الأمريكي'),
        ('AED', 'الدرهم الإماراتي'),
        ('QAR', 'الريال القطري'),
        ('EUR', 'اليورو'),
    ]

    CURRENCY_SYMBOLS = {
        'IQD': 'د.ع',
        'USD': '$',
        'AED': 'د.إ',
        'QAR': 'ر.ق',
        'EUR': '€',
    }

    code = models.CharField(max_length=3, choices=SUPPORTED_CURRENCIES, unique=True, verbose_name="رمز العملة")
    name = models.CharField(max_length=100, verbose_name="اسم العملة")
    name_en = models.CharField(max_length=100, verbose_name="اسم العملة بالإنجليزية")
    symbol = models.CharField(max_length=10, verbose_name="رمز العملة")
    exchange_rate = models.DecimalField(
        max_digits=15, decimal_places=6, default=Decimal('1.000000'),
        verbose_name="سعر الصرف مقابل العملة الأساسية"
    )
    is_base_currency = models.BooleanField(default=False, verbose_name="العملة الأساسية")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    last_updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='currency_updates', verbose_name="آخر تحديث بواسطة"
    )
    rate_updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ تحديث السعر")

    class Meta:
        verbose_name = "عملة"
        verbose_name_plural = "العملات"
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"

    def save(self, *args, **kwargs):
        # تعيين الاسم والرمز تلقائياً
        if self.code in dict(self.SUPPORTED_CURRENCIES):
            self.name = dict(self.SUPPORTED_CURRENCIES)[self.code]
            self.symbol = self.CURRENCY_SYMBOLS.get(self.code, self.code)

        if self.is_base_currency:
            # التأكد من وجود عملة أساسية واحدة فقط
            Currency.objects.filter(is_base_currency=True).exclude(pk=self.pk).update(is_base_currency=False)
            self.exchange_rate = Decimal('1.000000')
        super().save(*args, **kwargs)

    def convert_to_base(self, amount):
        """تحويل المبلغ إلى العملة الأساسية"""
        if self.is_base_currency:
            return amount
        return amount / self.exchange_rate

    def convert_from_base(self, amount):
        """تحويل المبلغ من العملة الأساسية"""
        if self.is_base_currency:
            return amount
        return amount * self.exchange_rate

    @classmethod
    def get_base_currency(cls):
        """الحصول على العملة الأساسية"""
        return cls.objects.filter(is_base_currency=True).first()

    @classmethod
    def convert_amount(cls, amount, from_currency, to_currency):
        """تحويل مبلغ من عملة إلى أخرى"""
        if from_currency == to_currency:
            return amount

        # تحويل إلى العملة الأساسية أولاً
        base_amount = from_currency.convert_to_base(amount)
        # ثم تحويل إلى العملة المطلوبة
        return to_currency.convert_from_base(base_amount)

class TaxType(BaseModel):
    """أنواع الضرائب المحسنة"""
    TAX_CALCULATION_TYPES = [
        ('percentage', 'نسبة مئوية'),
        ('fixed', 'مبلغ ثابت'),
        ('compound', 'ضريبة مركبة'),
    ]

    TAX_STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
    ]

    name = models.CharField(max_length=100, verbose_name="اسم الضريبة")
    name_en = models.CharField(max_length=100, verbose_name="اسم الضريبة بالإنجليزية", blank=True)
    code = models.CharField(max_length=20, unique=True, verbose_name="رمز الضريبة")
    rate = models.DecimalField(
        max_digits=5, decimal_places=2, default=Decimal('0.00'),
        verbose_name="معدل الضريبة"
    )
    calculation_type = models.CharField(
        max_length=20, choices=TAX_CALCULATION_TYPES,
        default='percentage', verbose_name="نوع الحساب"
    )
    is_inclusive = models.BooleanField(default=False, verbose_name="ضريبة شاملة")
    is_default = models.BooleanField(default=False, verbose_name="ضريبة افتراضية")
    status = models.CharField(
        max_length=20, choices=TAX_STATUS_CHOICES,
        default='active', verbose_name="حالة الضريبة"
    )
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    description = models.TextField(verbose_name="الوصف", blank=True)

    # تواريخ الصلاحية
    valid_from = models.DateField(null=True, blank=True, verbose_name="صالح من")
    valid_to = models.DateField(null=True, blank=True, verbose_name="صالح حتى")

    class Meta:
        verbose_name = "نوع ضريبة"
        verbose_name_plural = "أنواع الضرائب"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.rate}%)"

    def save(self, *args, **kwargs):
        # تحديث is_active بناءً على status
        self.is_active = (self.status == 'active')

        # التأكد من وجود ضريبة افتراضية واحدة فقط
        if self.is_default and self.is_active:
            TaxType.objects.filter(is_default=True).exclude(pk=self.pk).update(is_default=False)
        super().save(*args, **kwargs)

    def calculate_tax(self, amount):
        """حساب مبلغ الضريبة"""
        if not self.is_active:
            return Decimal('0.00')

        if self.calculation_type == 'percentage':
            return amount * (self.rate / 100)
        elif self.calculation_type == 'fixed':
            return self.rate
        elif self.calculation_type == 'compound':
            # حساب الضريبة المركبة (ضريبة على الضريبة)
            base_tax = amount * (self.rate / 100)
            return base_tax + (base_tax * (self.rate / 100))
        return Decimal('0.00')

    @classmethod
    def get_active_taxes(cls):
        """الحصول على الضرائب النشطة فقط"""
        return cls.objects.filter(status='active', is_active=True)

    @classmethod
    def get_default_tax(cls):
        """الحصول على الضريبة الافتراضية"""
        return cls.objects.filter(is_default=True, status='active', is_active=True).first()

class InvoiceTemplate(BaseModel):
    """قوالب الفواتير"""
    name = models.CharField(max_length=100, verbose_name="اسم القالب")
    description = models.TextField(verbose_name="الوصف", blank=True)

    # إعدادات التصميم
    header_color = models.CharField(max_length=7, default='#007bff', verbose_name="لون الرأس")
    font_family = models.CharField(
        max_length=50, default='Cairo',
        choices=[
            ('Cairo', 'Cairo'),
            ('Amiri', 'Amiri'),
            ('Tajawal', 'Tajawal'),
            ('Almarai', 'Almarai'),
        ],
        verbose_name="نوع الخط"
    )
    font_size = models.IntegerField(default=14, verbose_name="حجم الخط")

    # إعدادات المحتوى
    show_logo = models.BooleanField(default=True, verbose_name="إظهار الشعار")
    show_company_details = models.BooleanField(default=True, verbose_name="إظهار تفاصيل الشركة")
    show_customer_details = models.BooleanField(default=True, verbose_name="إظهار تفاصيل العميل")
    show_payment_terms = models.BooleanField(default=True, verbose_name="إظهار شروط الدفع")
    show_notes = models.BooleanField(default=True, verbose_name="إظهار الملاحظات")
    show_signature = models.BooleanField(default=False, verbose_name="إظهار التوقيع")

    # نص مخصص
    header_text = models.TextField(verbose_name="نص الرأس", blank=True)
    footer_text = models.TextField(verbose_name="نص التذييل", blank=True)

    is_default = models.BooleanField(default=False, verbose_name="قالب افتراضي")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    class Meta:
        verbose_name = "قالب فاتورة"
        verbose_name_plural = "قوالب الفواتير"
        ordering = ['name']

    def __str__(self):
        return self.name

class ExchangeRateHistory(BaseModel):
    """تاريخ أسعار الصرف"""
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE, verbose_name="العملة")
    rate = models.DecimalField(max_digits=10, decimal_places=4, verbose_name="سعر الصرف")
    date = models.DateField(verbose_name="التاريخ")
    source = models.CharField(
        max_length=50,
        choices=[
            ('manual', 'يدوي'),
            ('api', 'تلقائي من API'),
            ('bank', 'من البنك'),
        ],
        default='manual',
        verbose_name="المصدر"
    )

    class Meta:
        verbose_name = "تاريخ سعر الصرف"
        verbose_name_plural = "تاريخ أسعار الصرف"
        unique_together = ['currency', 'date']
        ordering = ['-date']

    def __str__(self):
        return f"{self.currency.code} - {self.rate} ({self.date})"

class SystemSettings(BaseModel):
    """إعدادات النظام العامة"""
    # إعدادات API أسعار الصرف
    exchange_rate_api_key = models.CharField(
        max_length=100, verbose_name="مفتاح API أسعار الصرف", blank=True
    )
    auto_update_exchange_rates = models.BooleanField(
        default=False, verbose_name="تحديث أسعار الصرف تلقائياً"
    )
    exchange_rate_update_frequency = models.CharField(
        max_length=20,
        choices=[
            ('daily', 'يومي'),
            ('weekly', 'أسبوعي'),
            ('monthly', 'شهري'),
        ],
        default='daily',
        verbose_name="تكرار تحديث أسعار الصرف"
    )

    # إعدادات النسخ الاحتياطي
    auto_backup_enabled = models.BooleanField(default=False, verbose_name="النسخ الاحتياطي التلقائي")
    backup_frequency = models.CharField(
        max_length=20,
        choices=[
            ('daily', 'يومي'),
            ('weekly', 'أسبوعي'),
            ('monthly', 'شهري'),
        ],
        default='weekly',
        verbose_name="تكرار النسخ الاحتياطي"
    )
    backup_retention_days = models.IntegerField(default=30, verbose_name="مدة الاحتفاظ بالنسخ (أيام)")

    # إعدادات التذكيرات
    reminder_enabled = models.BooleanField(default=True, verbose_name="تفعيل التذكيرات")
    reminder_days_before = models.CharField(
        max_length=50, default='7,3,1',
        verbose_name="أيام التذكير قبل الاستحقاق (مفصولة بفاصلة)"
    )
    reminder_days_after = models.CharField(
        max_length=50, default='1,7,14',
        verbose_name="أيام التذكير بعد الاستحقاق (مفصولة بفاصلة)"
    )

    # إعدادات WhatsApp
    whatsapp_enabled = models.BooleanField(default=False, verbose_name="تفعيل WhatsApp")
    whatsapp_api_token = models.CharField(max_length=200, verbose_name="رمز WhatsApp API", blank=True)
    whatsapp_phone_number = models.CharField(max_length=20, verbose_name="رقم WhatsApp", blank=True)

    class Meta:
        verbose_name = "إعدادات النظام"
        verbose_name_plural = "إعدادات النظام"

    def __str__(self):
        return "إعدادات النظام"

    def get_reminder_days_before_list(self):
        """الحصول على قائمة أيام التذكير قبل الاستحقاق"""
        if self.reminder_days_before:
            return [int(day.strip()) for day in self.reminder_days_before.split(',') if day.strip().isdigit()]
        return []

    def get_reminder_days_after_list(self):
        """الحصول على قائمة أيام التذكير بعد الاستحقاق"""
        if self.reminder_days_after:
            return [int(day.strip()) for day in self.reminder_days_after.split(',') if day.strip().isdigit()]
        return []

class DefaultSettings(BaseModel):
    """الإعدادات الافتراضية للنظام"""

    # إعدادات الضرائب الافتراضية
    default_tax_type = models.ForeignKey(
        TaxType, on_delete=models.SET_NULL, null=True, blank=True,
        verbose_name="نوع الضريبة الافتراضي"
    )
    auto_apply_tax = models.BooleanField(default=True, verbose_name="تطبيق الضريبة تلقائياً")

    # إعدادات شروط الدفع الافتراضية
    default_payment_terms_days = models.IntegerField(default=30, verbose_name="أيام الدفع الافتراضية")
    default_payment_method = models.CharField(
        max_length=20,
        choices=[
            ('cash', 'نقدي'),
            ('bank_transfer', 'تحويل بنكي'),
            ('check', 'شيك'),
            ('credit_card', 'بطاقة ائتمان'),
            ('online', 'دفع إلكتروني'),
        ],
        default='cash',
        verbose_name="طريقة الدفع الافتراضية"
    )

    # إعدادات ترقيم الفواتير
    sales_invoice_prefix = models.CharField(max_length=10, default='INV-S', verbose_name="بادئة فاتورة البيع")
    purchase_invoice_prefix = models.CharField(max_length=10, default='INV-P', verbose_name="بادئة فاتورة الشراء")
    payment_prefix = models.CharField(max_length=10, default='PAY', verbose_name="بادئة المدفوعة")
    invoice_number_length = models.IntegerField(default=6, verbose_name="طول رقم الفاتورة")

    # إعدادات العملة والعرض
    default_currency = models.ForeignKey(
        Currency, on_delete=models.PROTECT, null=True, blank=True,
        verbose_name="العملة الافتراضية"
    )
    decimal_places = models.IntegerField(default=2, verbose_name="عدد الخانات العشرية")
    currency_position = models.CharField(
        max_length=10,
        choices=[('before', 'قبل المبلغ'), ('after', 'بعد المبلغ')],
        default='after',
        verbose_name="موضع رمز العملة"
    )

    # إعدادات التقارير الافتراضية
    default_report_period = models.CharField(
        max_length=20,
        choices=[
            ('current_month', 'الشهر الحالي'),
            ('last_month', 'الشهر الماضي'),
            ('current_quarter', 'الربع الحالي'),
            ('current_year', 'السنة الحالية'),
            ('last_year', 'السنة الماضية'),
        ],
        default='current_month',
        verbose_name="فترة التقرير الافتراضية"
    )

    # إعدادات واجهة المستخدم
    items_per_page = models.IntegerField(default=25, verbose_name="عدد العناصر في الصفحة")
    date_format = models.CharField(
        max_length=20,
        choices=[
            ('d/m/Y', 'يوم/شهر/سنة'),
            ('Y-m-d', 'سنة-شهر-يوم'),
            ('d-m-Y', 'يوم-شهر-سنة'),
        ],
        default='d/m/Y',
        verbose_name="تنسيق التاريخ"
    )

    # إعدادات التذكيرات
    enable_due_date_reminders = models.BooleanField(default=True, verbose_name="تفعيل تذكيرات الاستحقاق")
    reminder_days_before_due = models.CharField(
        max_length=50, default='7,3,1',
        verbose_name="أيام التذكير قبل الاستحقاق"
    )

    class Meta:
        verbose_name = "الإعدادات الافتراضية"
        verbose_name_plural = "الإعدادات الافتراضية"

    def __str__(self):
        return "الإعدادات الافتراضية"

    @classmethod
    def get_settings(cls):
        """الحصول على الإعدادات الافتراضية أو إنشاؤها"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings
