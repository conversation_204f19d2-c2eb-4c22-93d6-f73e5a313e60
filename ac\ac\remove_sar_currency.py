#!/usr/bin/env python
"""
حذف الريال السعودي من النظام والتأكد من أن الدينار العراقي هو العملة الوحيدة
"""

import os
import sys
import django

# إعداد Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'accounting_system.settings')
django.setup()

from core.models import Currency
from accounts.models import Customer, Supplier
from decimal import Decimal

def remove_sar_currency():
    """حذف الريال السعودي من النظام"""
    print("🗑️ حذف الريال السعودي من النظام...")
    
    # البحث عن الريال السعودي
    sar_currencies = Currency.objects.filter(code='SAR')
    
    if sar_currencies.exists():
        count = sar_currencies.count()
        print(f"وجدت {count} عملة ريال سعودي")
        
        # حذف الريال السعودي
        sar_currencies.delete()
        print("✅ تم حذف الريال السعودي بنجاح")
    else:
        print("ℹ️ لا يوجد ريال سعودي في النظام")

def ensure_iraqi_dinar():
    """التأكد من وجود الدينار العراقي كعملة أساسية"""
    print("\n💰 التأكد من الدينار العراقي...")
    
    # إنشاء أو تحديث الدينار العراقي
    iqd, created = Currency.objects.get_or_create(
        code='IQD',
        defaults={
            'name': 'الدينار العراقي',
            'name_en': 'Iraqi Dinar',
            'symbol': 'د.ع',
            'exchange_rate': Decimal('1.000000'),
            'is_base_currency': True,
            'is_active': True
        }
    )
    
    if created:
        print("✅ تم إنشاء الدينار العراقي كعملة أساسية")
    else:
        # تحديث الدينار العراقي
        iqd.is_base_currency = True
        iqd.is_active = True
        iqd.exchange_rate = Decimal('1.000000')
        iqd.save()
        print("✅ تم تحديث الدينار العراقي كعملة أساسية")

def update_currency_model():
    """تحديث نموذج العملة لإزالة الريال السعودي"""
    print("\n🔧 تحديث نموذج العملة...")
    
    # التأكد من عدم وجود عملات أساسية متعددة
    base_currencies = Currency.objects.filter(is_base_currency=True)
    if base_currencies.count() > 1:
        # إبقاء الدينار العراقي فقط كعملة أساسية
        base_currencies.exclude(code='IQD').update(is_base_currency=False)
        print("✅ تم تحديث العملات الأساسية")

def display_remaining_currencies():
    """عرض العملات المتبقية في النظام"""
    print("\n📋 العملات المتبقية في النظام:")
    
    currencies = Currency.objects.all().order_by('name')
    
    if currencies.exists():
        for currency in currencies:
            status = "أساسية" if currency.is_base_currency else "ثانوية"
            active = "نشطة" if currency.is_active else "غير نشطة"
            print(f"  - {currency.code}: {currency.name} ({currency.symbol}) - {status}, {active}")
    else:
        print("  لا توجد عملات في النظام")

def main():
    """تشغيل عملية حذف الريال السعودي"""
    print("🇮🇶 بدء عملية حذف الريال السعودي من النظام المحاسبي العراقي")
    print("=" * 70)
    
    try:
        remove_sar_currency()
        ensure_iraqi_dinar()
        update_currency_model()
        display_remaining_currencies()
        
        print("\n" + "=" * 70)
        print("✅ تم حذف الريال السعودي بنجاح!")
        print("🇮🇶 النظام يستخدم الآن الدينار العراقي فقط")
        
    except Exception as e:
        print(f"\n❌ خطأ في حذف الريال السعودي: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
