#!/usr/bin/env python
"""
Simple server starter for the accounting system
"""

import os
import sys
import subprocess

def main():
    try:
        # Set Django settings
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'accounting_system.settings')
        
        # Import Django
        import django
        django.setup()
        
        print("✅ Django setup successful")
        
        # Run migrations
        print("🔄 Running migrations...")
        subprocess.run([sys.executable, 'manage.py', 'migrate'], check=True)
        print("✅ Migrations completed")
        
        # Start server
        print("🚀 Starting server...")
        print("🌐 Server will be available at: http://127.0.0.1:8000/")
        print("👤 Login: admin / admin123")
        print("📊 Reports: http://127.0.0.1:8000/reports/")
        print("\n" + "="*50)
        
        subprocess.run([sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000'])
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Try installing Django: pip install django")
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
