<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير ملخص المشتريات</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }
        
        .report-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
        }
        
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #dc3545;
        }
        
        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 10px;
        }
        
        .report-period {
            font-size: 1.2rem;
            color: #6c757d;
            margin: 0;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        
        .company-name {
            font-size: 1.5rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 5px;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-box {
            text-align: center;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #dc3545;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #dc3545;
            margin: 30px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #dc3545;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .data-table th {
            background-color: #dc3545;
            color: white;
            font-weight: 600;
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #b02a37;
            font-size: 0.9rem;
        }
        
        .data-table td {
            padding: 10px 8px;
            border: 1px solid #dee2e6;
            text-align: center;
            font-size: 0.85rem;
        }
        
        .data-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .data-table tfoot th {
            background-color: #e9ecef;
            color: #212529;
            font-weight: 700;
        }
        
        .text-end {
            text-align: left !important;
        }
        
        .text-right {
            text-align: right !important;
        }
        
        .currency-amount {
            font-weight: 600;
            color: #dc3545;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-paid { background-color: #d1e7dd; color: #0f5132; }
        .status-pending { background-color: #fff3cd; color: #664d03; }
        .status-draft { background-color: #e2e3e5; color: #41464b; }
        .status-partial { background-color: #cff4fc; color: #055160; }
        .status-overdue { background-color: #f8d7da; color: #721c24; }
        .status-cancelled { background-color: #d3d3d4; color: #495057; }
        
        .footer-section {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 0;
                font-size: 12px;
            }
            
            .report-container {
                max-width: none;
                margin: 0;
                padding: 15mm;
            }
            
            .report-header {
                page-break-inside: avoid;
            }
            
            .summary-stats {
                page-break-inside: avoid;
            }
            
            .data-table {
                page-break-inside: auto;
            }
            
            .data-table tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
            
            .section-title {
                page-break-after: avoid;
            }
            
            .footer-section {
                page-break-inside: avoid;
            }
            
            @page {
                margin: 15mm;
                size: A4;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- رأس التقرير -->
        <div class="report-header">
            <h1 class="report-title">تقرير ملخص المشتريات</h1>
            <p class="report-period">
                من {{ start_date|date:"d/m/Y" }} إلى {{ end_date|date:"d/m/Y" }}
            </p>
        </div>
        
        <!-- معلومات الشركة -->
        <div class="company-info">
            <h2 class="company-name">{{ company.company_name }}</h2>
            {% if company.company_name_en %}
                <p class="text-muted mb-1">{{ company.company_name_en }}</p>
            {% endif %}
            <p class="mb-1">{{ company.address }}</p>
            <p class="mb-0">هاتف: {{ company.phone }} | بريد إلكتروني: {{ company.email }}</p>
        </div>
        
        <!-- الإحصائيات الملخصة -->
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-label">إجمالي الفواتير</div>
                <div class="stat-value">{{ invoices_count|default:0 }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">إجمالي المشتريات</div>
                <div class="stat-value">{{ total_purchases|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">متوسط قيمة الفاتورة</div>
                <div class="stat-value">{{ average_invoice_value|floatformat:2|default:0 }} {{ base_currency_symbol|default:"د.ع" }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">عدد الموردين</div>
                <div class="stat-value">{{ unique_suppliers|default:0 }}</div>
            </div>
        </div>
        
        <!-- أفضل الموردين -->
        {% if top_suppliers %}
        <h2 class="section-title">أفضل الموردين</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 10%">#</th>
                    <th style="width: 40%">اسم المورد</th>
                    <th style="width: 20%">عدد الفواتير</th>
                    <th style="width: 30%">إجمالي المشتريات</th>
                </tr>
            </thead>
            <tbody>
                {% for supplier in top_suppliers %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td class="text-right">{{ supplier.supplier__name }}</td>
                    <td>{{ supplier.invoices_count }}</td>
                    <td class="text-end currency-amount">{{ supplier.total_amount|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endif %}
        
        <!-- أفضل المنتجات المشتراة -->
        {% if top_purchased_items %}
        <h2 class="section-title">أفضل المنتجات المشتراة</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 10%">#</th>
                    <th style="width: 40%">اسم المنتج</th>
                    <th style="width: 20%">الكمية المشتراة</th>
                    <th style="width: 30%">إجمالي المشتريات</th>
                </tr>
            </thead>
            <tbody>
                {% for item in top_purchased_items %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td class="text-right">{{ item.description }}</td>
                    <td>{{ item.total_quantity|floatformat:0 }}</td>
                    <td class="text-end currency-amount">{{ item.total_amount|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endif %}
        
        <!-- تفاصيل الفواتير -->
        {% if invoices %}
        <h2 class="section-title">تفاصيل الفواتير</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 15%">رقم الفاتورة</th>
                    <th style="width: 12%">التاريخ</th>
                    <th style="width: 25%">المورد</th>
                    <th style="width: 10%">البنود</th>
                    <th style="width: 18%">المبلغ الإجمالي</th>
                    <th style="width: 20%">الحالة</th>
                </tr>
            </thead>
            <tbody>
                {% for invoice in invoices %}
                <tr>
                    <td class="text-right">{{ invoice.invoice_number }}</td>
                    <td>{{ invoice.invoice_date|date:"d/m/Y" }}</td>
                    <td class="text-right">{{ invoice.supplier.name }}</td>
                    <td>{{ invoice.items.count }}</td>
                    <td class="text-end currency-amount">{{ invoice.total_amount|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</td>
                    <td>
                        {% if invoice.status == 'draft' %}
                            <span class="status-badge status-draft">مسودة</span>
                        {% elif invoice.status == 'pending' %}
                            <span class="status-badge status-pending">معلقة</span>
                        {% elif invoice.status == 'paid' %}
                            <span class="status-badge status-paid">مدفوعة</span>
                        {% elif invoice.status == 'partial' %}
                            <span class="status-badge status-partial">مدفوعة جزئياً</span>
                        {% elif invoice.status == 'overdue' %}
                            <span class="status-badge status-overdue">متأخرة</span>
                        {% elif invoice.status == 'cancelled' %}
                            <span class="status-badge status-cancelled">ملغية</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr>
                    <th colspan="4" class="text-end">الإجمالي:</th>
                    <th class="text-end currency-amount">{{ total_purchases|floatformat:2 }} {{ base_currency_symbol|default:"د.ع" }}</th>
                    <th></th>
                </tr>
            </tfoot>
        </table>
        {% endif %}
        
        <!-- تذييل التقرير -->
        <div class="footer-section">
            <p class="mb-1">تم إنشاء هذا التقرير في {{ current_date|date:"d/m/Y H:i" }}</p>
            <p class="mb-0">{{ company.company_name }} - نظام الفواتير العراقي</p>
        </div>
    </div>
    
    <!-- أزرار الطباعة -->
    <div class="no-print" style="text-align: center; margin: 30px 0;">
        <button onclick="window.print()" style="background: #dc3545; color: white; border: none; padding: 12px 24px; border-radius: 5px; font-size: 16px; margin: 0 10px; cursor: pointer;">
            طباعة التقرير
        </button>
        <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 5px; font-size: 16px; margin: 0 10px; cursor: pointer;">
            إغلاق
        </button>
    </div>
</body>
</html>
