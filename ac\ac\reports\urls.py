from django.urls import path
from . import views

app_name = 'reports'

urlpatterns = [
    path('', views.ReportsIndexView.as_view(), name='index'),

    # تقارير الأرصدة
    path('customer-balances/', views.CustomerBalancesReportView.as_view(), name='customer_balances'),
    path('supplier-balances/', views.SupplierBalancesReportView.as_view(), name='supplier_balances'),
    path('supplier-balances/print/', views.SupplierBalancesPrintView.as_view(), name='supplier_balances_print'),

    # تقارير المبيعات والمشتريات
    path('sales-summary/', views.SalesSummaryReportView.as_view(), name='sales_summary'),
    path('sales-summary/print/', views.SalesSummaryPrintView.as_view(), name='sales_summary_print'),
    path('purchase-summary/', views.PurchaseSummaryReportView.as_view(), name='purchase_summary'),
    path('purchase-summary/print/', views.PurchaseSummaryPrintView.as_view(), name='purchase_summary_print'),

    # التقارير المالية
    path('cash-flow/', views.CashFlowReportView.as_view(), name='cash_flow'),
    path('tax-report/', views.TaxReportView.as_view(), name='tax_report'),
    path('detailed-tax/', views.DetailedTaxReportView.as_view(), name='detailed_tax'),
    path('detailed-tax/print/', views.DetailedTaxPrintView.as_view(), name='detailed_tax_print'),
    path('profit-loss/', views.ProfitLossReportView.as_view(), name='profit_loss'),
    path('profit-loss/print/', views.ProfitLossPrintView.as_view(), name='profit_loss_print'),
    path('balance-sheet/', views.BalanceSheetReportView.as_view(), name='balance_sheet'),
    path('balance-sheet/print/', views.BalanceSheetPrintView.as_view(), name='balance_sheet_print'),
    path('currency-report/', views.CurrencyReportView.as_view(), name='currency_report'),
    path('currency-report/print/', views.CurrencyReportPrintView.as_view(), name='currency_report_print'),
    path('overdue-invoices/', views.OverdueInvoicesReportView.as_view(), name='overdue_invoices'),
    path('overdue-invoices/print/', views.OverdueInvoicesPrintView.as_view(), name='overdue_invoices_print'),
    path('balance-sheet/', views.BalanceSheetReportView.as_view(), name='balance_sheet'),

    # تصدير التقارير CSV
    path('customer-balances/export/', views.CustomerBalancesExportView.as_view(), name='customer_balances_export'),
    path('supplier-balances/export/', views.SupplierBalancesExportView.as_view(), name='supplier_balances_export'),
    path('cash-flow/export/', views.CashFlowExportView.as_view(), name='cash_flow_export'),
    path('sales-summary/export/', views.SalesSummaryExportView.as_view(), name='sales_summary_export'),

    # تصدير التقارير Excel
    path('customer-balances/excel/', views.CustomerBalancesExcelView.as_view(), name='customer_balances_excel'),
    path('sales-summary/excel/', views.SalesSummaryExcelView.as_view(), name='sales_summary_excel'),
    path('purchase-summary/excel/', views.PurchaseSummaryExcelView.as_view(), name='purchase_summary_excel'),
    path('tax-report/excel/', views.TaxReportExcelView.as_view(), name='tax_report_excel'),

    # التقارير المخصصة
    path('overdue-invoices/', views.OverdueInvoicesReportView.as_view(), name='overdue_invoices'),
    path('currency-report/', views.CurrencyReportView.as_view(), name='currency_report'),
    path('detailed-tax-report/', views.DetailedTaxReportView.as_view(), name='detailed_tax_report'),
]
