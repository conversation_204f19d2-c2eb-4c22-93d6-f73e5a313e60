from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.contrib import messages
from django.db.models import Q, Sum
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.core.mail import EmailMessage
from django.conf import settings
from django.db.models import Q, Sum
from decimal import Decimal
import json
from .models import SalesInvoice, PurchaseInvoice, InvoiceItem
from .forms import SalesInvoiceForm, PurchaseInvoiceForm, InvoiceItemFormSet, PurchaseInvoiceItemFormSet
from accounts.models import Customer, Supplier

# ========== فواتير البيع ==========
class SalesInvoiceListView(LoginRequiredMixin, ListView):
    model = SalesInvoice
    template_name = 'invoices/sales_list.html'
    context_object_name = 'invoices'
    paginate_by = 20

    def get_queryset(self):
        queryset = SalesInvoice.objects.select_related('customer').order_by('-created_at')
        search = self.request.GET.get('search')
        status = self.request.GET.get('status')

        if search:
            queryset = queryset.filter(
                Q(invoice_number__icontains=search) |
                Q(customer__name__icontains=search) |
                Q(customer__customer_code__icontains=search)
            )

        if status:
            queryset = queryset.filter(status=status)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search'] = self.request.GET.get('search', '')
        context['status'] = self.request.GET.get('status', '')
        context['status_choices'] = SalesInvoice.INVOICE_STATUS
        return context

class SalesInvoiceDetailView(LoginRequiredMixin, DetailView):
    model = SalesInvoice
    template_name = 'invoices/sales_detail.html'
    context_object_name = 'invoice'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        invoice = self.get_object()
        context['items'] = invoice.items.all()
        context['paid_amount'] = invoice.get_paid_amount()
        context['remaining_amount'] = invoice.get_remaining_amount()
        return context

class SalesInvoiceCreateView(LoginRequiredMixin, CreateView):
    model = SalesInvoice
    form_class = SalesInvoiceForm
    template_name = 'invoices/sales_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.POST:
            context['formset'] = InvoiceItemFormSet(self.request.POST, prefix='items')
        else:
            context['formset'] = InvoiceItemFormSet(prefix='items')
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']

        if formset.is_valid():
            self.object = form.save()

            # حفظ البنود
            for item_form in formset:
                if item_form.cleaned_data and not item_form.cleaned_data.get('DELETE', False):
                    item = item_form.save(commit=False)
                    item.sales_invoice = self.object
                    item.save()

            # حساب الإجماليات
            self.object.calculate_totals()
            messages.success(self.request, 'تم إنشاء فاتورة البيع بنجاح')
            return redirect('invoices:sales_detail', pk=self.object.pk)
        else:
            return self.form_invalid(form)

class SalesInvoiceUpdateView(LoginRequiredMixin, UpdateView):
    model = SalesInvoice
    form_class = SalesInvoiceForm
    template_name = 'invoices/sales_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.POST:
            context['formset'] = InvoiceItemFormSet(
                self.request.POST,
                instance=self.object,
                prefix='items'
            )
        else:
            context['formset'] = InvoiceItemFormSet(
                instance=self.object,
                prefix='items'
            )
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']

        if formset.is_valid():
            self.object = form.save()
            formset.save()
            self.object.calculate_totals()
            messages.success(self.request, 'تم تحديث فاتورة البيع بنجاح')
            return redirect('invoices:sales_detail', pk=self.object.pk)
        else:
            return self.form_invalid(form)

class SalesInvoiceDeleteView(LoginRequiredMixin, DeleteView):
    model = SalesInvoice
    template_name = 'invoices/sales_confirm_delete.html'
    success_url = reverse_lazy('invoices:sales_list')

    def delete(self, request, *args, **kwargs):
        invoice = self.get_object()
        if invoice.status == 'paid':
            messages.error(request, 'لا يمكن حذف فاتورة مدفوعة')
            return redirect('invoices:sales_detail', pk=invoice.pk)

        messages.success(request, 'تم حذف فاتورة البيع بنجاح')
        return super().delete(request, *args, **kwargs)

class SalesInvoicePrintView(LoginRequiredMixin, DetailView):
    model = SalesInvoice
    template_name = 'invoices/sales_print.html'
    context_object_name = 'invoice'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from core.models import CompanySettings
        context['company'] = CompanySettings.objects.first()
        context['items'] = self.get_object().items.all()
        return context

# ========== فواتير الشراء ==========
class PurchaseInvoiceListView(LoginRequiredMixin, ListView):
    model = PurchaseInvoice
    template_name = 'invoices/purchase_list.html'
    context_object_name = 'invoices'
    paginate_by = 20

    def get_queryset(self):
        queryset = PurchaseInvoice.objects.select_related('supplier').order_by('-created_at')
        search = self.request.GET.get('search')
        status = self.request.GET.get('status')
        supplier = self.request.GET.get('supplier')
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')

        if search:
            queryset = queryset.filter(
                Q(invoice_number__icontains=search) |
                Q(supplier_invoice_number__icontains=search) |
                Q(supplier__name__icontains=search) |
                Q(supplier__supplier_code__icontains=search)
            )

        if status:
            queryset = queryset.filter(status=status)

        if supplier:
            queryset = queryset.filter(supplier__name__icontains=supplier)

        if start_date:
            queryset = queryset.filter(invoice_date__gte=start_date)

        if end_date:
            queryset = queryset.filter(invoice_date__lte=end_date)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # فلاتر البحث
        context['search'] = self.request.GET.get('search', '')
        context['status'] = self.request.GET.get('status', '')
        context['supplier'] = self.request.GET.get('supplier', '')
        context['start_date'] = self.request.GET.get('start_date', '')
        context['end_date'] = self.request.GET.get('end_date', '')
        context['status_choices'] = PurchaseInvoice.INVOICE_STATUS

        # حساب الإحصائيات
        all_invoices = self.get_queryset()
        context['total_invoices'] = all_invoices.count()
        context['total_amount'] = all_invoices.aggregate(
            total=Sum('total_amount')
        )['total'] or 0

        # إحصائيات الحالات
        context['paid_invoices'] = all_invoices.filter(status='paid').count()
        context['pending_invoices'] = all_invoices.filter(status='pending').count()

        # حساب المبالغ المدفوعة والمتبقية
        from payments.models import Payment
        total_paid = 0
        total_remaining = 0

        for invoice in all_invoices:
            paid_amount = Payment.objects.filter(
                purchase_invoice=invoice, payment_type='paid'
            ).aggregate(total=Sum('amount'))['total'] or 0

            total_paid += paid_amount
            total_remaining += (invoice.total_amount - paid_amount)

        context['total_paid'] = total_paid
        context['total_remaining'] = total_remaining

        # إضافة التاريخ الحالي للمقارنة
        from datetime import date
        context['today'] = date.today()

        return context

class PurchaseInvoiceDetailView(LoginRequiredMixin, DetailView):
    model = PurchaseInvoice
    template_name = 'invoices/purchase_detail.html'
    context_object_name = 'invoice'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        invoice = self.get_object()
        context['items'] = invoice.items.all()
        # إضافة دوال للمدفوعات في فواتير الشراء
        from payments.models import Payment
        from django.db.models import Sum
        paid_amount = Payment.objects.filter(
            purchase_invoice=invoice, payment_type='paid'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        context['paid_amount'] = paid_amount
        context['remaining_amount'] = invoice.total_amount - paid_amount
        return context

class PurchaseInvoiceCreateView(LoginRequiredMixin, CreateView):
    model = PurchaseInvoice
    form_class = PurchaseInvoiceForm
    template_name = 'invoices/purchase_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.POST:
            context['formset'] = PurchaseInvoiceItemFormSet(self.request.POST, prefix='items')
        else:
            context['formset'] = PurchaseInvoiceItemFormSet(prefix='items')
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']

        if formset.is_valid():
            self.object = form.save()

            # حفظ البنود
            for item_form in formset:
                if item_form.cleaned_data and not item_form.cleaned_data.get('DELETE', False):
                    item = item_form.save(commit=False)
                    item.purchase_invoice = self.object
                    item.save()

            messages.success(self.request, 'تم إنشاء فاتورة الشراء بنجاح')
            return redirect('invoices:purchase_detail', pk=self.object.pk)
        else:
            return self.form_invalid(form)

class PurchaseInvoiceUpdateView(LoginRequiredMixin, UpdateView):
    model = PurchaseInvoice
    form_class = PurchaseInvoiceForm
    template_name = 'invoices/purchase_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.POST:
            context['formset'] = PurchaseInvoiceItemFormSet(
                self.request.POST,
                instance=self.object,
                prefix='items'
            )
        else:
            context['formset'] = PurchaseInvoiceItemFormSet(
                instance=self.object,
                prefix='items'
            )
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']

        if formset.is_valid():
            self.object = form.save()
            formset.save()
            messages.success(self.request, 'تم تحديث فاتورة الشراء بنجاح')
            return redirect('invoices:purchase_detail', pk=self.object.pk)
        else:
            return self.form_invalid(form)

class PurchaseInvoiceDeleteView(LoginRequiredMixin, DeleteView):
    model = PurchaseInvoice
    template_name = 'invoices/purchase_confirm_delete.html'
    success_url = reverse_lazy('invoices:purchase_list')

    def delete(self, request, *args, **kwargs):
        invoice = self.get_object()
        if invoice.status == 'paid':
            messages.error(request, 'لا يمكن حذف فاتورة مدفوعة')
            return redirect('invoices:purchase_detail', pk=invoice.pk)

        messages.success(request, 'تم حذف فاتورة الشراء بنجاح')
        return super().delete(request, *args, **kwargs)

class PurchaseInvoicePrintView(LoginRequiredMixin, DetailView):
    model = PurchaseInvoice
    template_name = 'invoices/purchase_print.html'
    context_object_name = 'invoice'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from core.models import CompanySettings
        context['company'] = CompanySettings.objects.first()
        context['items'] = self.get_object().items.all()
        return context

class SalesInvoiceEmailView(LoginRequiredMixin, DetailView):
    """إرسال فاتورة البيع بالبريد الإلكتروني"""
    model = SalesInvoice

    def post(self, request, *args, **kwargs):
        invoice = self.get_object()

        try:
            # التحقق من وجود بريد إلكتروني للعميل
            if not invoice.customer.email:
                return JsonResponse({
                    'success': False,
                    'message': 'لا يوجد بريد إلكتروني للعميل'
                })

            # إنشاء محتوى البريد الإلكتروني
            subject = f'فاتورة رقم {invoice.invoice_number}'

            # رسالة نصية
            message = f"""
السلام عليكم ورحمة الله وبركاته

عزيزي/عزيزتي {invoice.customer.name}

نرسل لكم فاتورة رقم {invoice.invoice_number} بتاريخ {invoice.invoice_date}

تفاصيل الفاتورة:
- المبلغ الإجمالي: {invoice.total_amount} ر.س
- تاريخ الاستحقاق: {invoice.due_date}

يرجى مراجعة الفاتورة المرفقة والدفع في الموعد المحدد.

شكراً لتعاملكم معنا

مع تحيات فريق العمل
            """

            # إنشاء البريد الإلكتروني
            email = EmailMessage(
                subject=subject,
                body=message,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                to=[invoice.customer.email],
            )

            # إرسال البريد
            email.send()

            return JsonResponse({
                'success': True,
                'message': f'تم إرسال الفاتورة بنجاح إلى {invoice.customer.email}'
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'فشل في إرسال البريد الإلكتروني: {str(e)}'
            })

class SalesInvoicePaymentView(LoginRequiredMixin, DetailView):
    """تسجيل مدفوعة للفاتورة"""
    model = SalesInvoice

    def post(self, request, *args, **kwargs):
        invoice = self.get_object()

        try:
            amount = Decimal(request.POST.get('amount', '0'))
            payment_method = request.POST.get('payment_method', 'cash')
            notes = request.POST.get('notes', '')

            if amount <= 0:
                return JsonResponse({
                    'success': False,
                    'message': 'يجب أن يكون المبلغ أكبر من صفر'
                })

            remaining_amount = invoice.get_remaining_amount()
            if amount > remaining_amount:
                return JsonResponse({
                    'success': False,
                    'message': f'المبلغ أكبر من المبلغ المتبقي ({remaining_amount} ر.س)'
                })

            # إنشاء المدفوعة
            from payments.models import Payment
            payment = Payment.objects.create(
                customer=invoice.customer,
                sales_invoice=invoice,
                amount=amount,
                payment_date=request.POST.get('payment_date', invoice.invoice_date),
                payment_method=payment_method,
                payment_type='received',
                notes=notes
            )

            return JsonResponse({
                'success': True,
                'message': f'تم تسجيل المدفوعة بنجاح ({amount} ر.س)',
                'payment_id': payment.id,
                'remaining_amount': invoice.get_remaining_amount()
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'فشل في تسجيل المدفوعة: {str(e)}'
            })
