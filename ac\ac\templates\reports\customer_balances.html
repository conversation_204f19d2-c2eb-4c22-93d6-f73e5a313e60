{% extends 'base/base.html' %}
{% load humanize %}

{% block title %}تقرير أرصدة العملاء - النظام المحاسبي{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-users text-primary"></i>
                تقرير أرصدة العملاء
            </h1>
            <div>
                <a href="{% url 'reports:customer_balances_print' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                   class="btn btn-info" target="_blank">
                    <i class="fas fa-print me-2"></i>طباعة التقرير
                </a>
                <a href="{% url 'reports:customer_balances_export' %}?as_of_date={{ as_of_date|date:'Y-m-d' }}" class="btn btn-success">
                    <i class="fas fa-download me-2"></i>تصدير CSV
                </a>
                <a href="{% url 'reports:index' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر التقرير -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-filter me-2"></i>فلاتر التقرير
                </h6>
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="as_of_date" class="form-label">كما في تاريخ</label>
                        <input type="date" class="form-control" id="as_of_date" name="as_of_date" 
                               value="{{ as_of_date|date:'Y-m-d' }}">
                    </div>
                    <div class="col-md-4">
                        <label for="min_balance" class="form-label">الحد الأدنى للرصيد</label>
                        <input type="number" class="form-control" id="min_balance" name="min_balance" 
                               step="0.01" placeholder="0.00">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter me-2"></i>تطبيق الفلتر
                        </button>
                        <a href="{% url 'reports:customer_balances' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات سريعة -->
    <div class="col-md-4">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-chart-bar me-2"></i>ملخص الأرصدة
                </h6>
                <div class="row text-center">
                    <div class="col-12 mb-2">
                        <small class="text-muted">إجمالي العملاء</small>
                        <h5 class="text-primary mb-0">{{ balances|length }}</h5>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الأرصدة -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    أرصدة العملاء كما في {{ as_of_date|date:"d/m/Y" }}
                </h5>
            </div>
            <div class="card-body">
                {% if balances %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="balances-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>كود العميل</th>
                                    <th>اسم العميل</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th class="text-end">الرصيد الحالي</th>
                                    <th class="text-end">المبلغ المتأخر</th>
                                    <th class="text-center">عدد الفواتير</th>
                                    <th class="text-center">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for balance in balances %}
                                <tr>
                                    <td><strong>{{ balance.customer.customer_code }}</strong></td>
                                    <td>
                                        <a href="{% url 'accounts:customer_detail' balance.customer.pk %}" 
                                           class="text-decoration-none">
                                            {{ balance.customer.name }}
                                        </a>
                                    </td>
                                    <td>{{ balance.customer.email|default:"-" }}</td>
                                    <td>{{ balance.customer.phone }}</td>
                                    <td class="text-end">
                                        <span class="{% if balance.balance > 0 %}text-danger fw-bold{% elif balance.balance < 0 %}text-success{% else %}text-muted{% endif %}">
                                            {{ balance.balance|floatformat:2 }} {{ base_currency.symbol|default:"د.ع" }}
                                        </span>
                                    </td>
                                    <td class="text-end">
                                        {% if balance.overdue_amount > 0 %}
                                            <span class="text-danger fw-bold">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                {{ balance.overdue_amount|floatformat:2 }} {{ base_currency.symbol|default:"د.ع" }}
                                            </span>
                                        {% else %}
                                            <span class="text-success">
                                                <i class="fas fa-check me-1"></i>
                                                لا توجد متأخرات
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info">{{ balance.invoices_count }}</span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'accounts:customer_detail' balance.customer.pk %}" 
                                               class="btn btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if balance.balance > 0 %}
                                            <a href="{% url 'payments:received_create' %}?customer={{ balance.customer.pk }}" 
                                               class="btn btn-outline-success" title="تسجيل مدفوعة">
                                                <i class="fas fa-money-bill"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- معلومات إضافية -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>معلومات التقرير
                                </h6>
                                <p class="mb-1"><strong>تاريخ التقرير:</strong> {{ as_of_date|date:"d/m/Y" }}</p>
                                <p class="mb-1"><strong>عدد العملاء:</strong> {{ balances|length }}</p>
                                <p class="mb-0"><strong>تاريخ الإنشاء:</strong> {{ now|date:"d/m/Y H:i" }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-warning">
                                <h6 class="alert-heading">
                                    <i class="fas fa-exclamation-triangle me-2"></i>ملاحظات
                                </h6>
                                <ul class="mb-0">
                                    <li>الأرصدة الموجبة تعني مبالغ مستحقة على العميل</li>
                                    <li>الأرصدة السالبة تعني مبالغ مدفوعة مقدماً</li>
                                    <li>المبالغ المتأخرة هي الفواتير المتجاوزة لتاريخ الاستحقاق</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد بيانات أرصدة</h5>
                        <p class="text-muted">لا توجد أرصدة للعملاء في التاريخ المحدد</p>
                        <a href="{% url 'accounts:customer_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إضافة عميل جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function printReport() {
    window.print();
}

// تحسين عرض الجدول
document.addEventListener('DOMContentLoaded', function() {
    // إضافة فلترة للجدول
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.className = 'form-control mb-3';
    searchInput.placeholder = 'البحث في الجدول...';
    
    const table = document.getElementById('balances-table');
    if (table) {
        table.parentNode.insertBefore(searchInput, table);
        
        searchInput.addEventListener('keyup', function() {
            const filter = this.value.toLowerCase();
            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const cells = rows[i].getElementsByTagName('td');
                let found = false;
                
                for (let j = 0; j < cells.length; j++) {
                    if (cells[j].textContent.toLowerCase().includes(filter)) {
                        found = true;
                        break;
                    }
                }
                
                rows[i].style.display = found ? '' : 'none';
            }
        });
    }
});
</script>

<style>
@media print {
    .btn, .card-header, .alert {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
}
</style>
{% endblock %}
