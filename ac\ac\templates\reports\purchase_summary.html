{% extends 'base/base.html' %}
{% load humanize %}

{% block title %}ملخص المشتريات - التقارير{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-shopping-cart text-warning"></i>
                تقرير ملخص المشتريات
            </h1>
            <div>
                <a href="{% url 'reports:purchase_summary_excel' %}?start_date={{ start_date|date:'Y-m-d' }}&end_date={{ end_date|date:'Y-m-d' }}" class="btn btn-success">
                    <i class="fas fa-file-excel me-2"></i>تصدير Excel
                </a>
                <a href="{% url 'reports:purchase_summary_print' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                   class="btn btn-info" target="_blank">
                    <i class="fas fa-print me-2"></i>طباعة التقرير
                </a>
                <a href="{% url 'reports:index' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- فلتر التواريخ -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="start_date" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="{{ start_date|date:'Y-m-d' }}">
                    </div>
                    <div class="col-md-4">
                        <label for="end_date" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="{{ end_date|date:'Y-m-d' }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block">
                            <i class="fas fa-search me-2"></i>عرض التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- ملخص المشتريات -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">إجمالي المشتريات</h4>
                        <h2>{{ total_purchases|floatformat:0|intcomma }} د.ع</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-shopping-cart fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">إجمالي الضرائب</h4>
                        <h2>{{ total_tax|floatformat:0|intcomma }} د.ع</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-percentage fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">عدد الفواتير</h4>
                        <h2>{{ invoices_count|intcomma }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-invoice fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- المشتريات حسب الموردين -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-truck me-2"></i>المشتريات حسب الموردين
                </h5>
            </div>
            <div class="card-body">
                {% if purchases_by_supplier %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم المورد</th>
                                <th>إجمالي المشتريات</th>
                                <th>عدد الفواتير</th>
                                <th>متوسط الفاتورة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for purchase in purchases_by_supplier %}
                            <tr>
                                <td>{{ purchase.supplier__name }}</td>
                                <td>{{ purchase.total_amount|floatformat:0|intcomma }} د.ع</td>
                                <td>{{ purchase.invoices_count|intcomma }}</td>
                                <td>
                                    {% widthratio purchase.total_amount purchase.invoices_count 1 as avg %}
                                    {{ avg|floatformat:0|intcomma }} د.ع
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد مشتريات في الفترة المحددة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- المشتريات حسب الشهر -->
{% if purchases_by_month %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>المشتريات حسب الشهر
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>الشهر</th>
                                <th>إجمالي المشتريات</th>
                                <th>عدد الفواتير</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for month_data in purchases_by_month %}
                            <tr>
                                <td>{{ month_data.month }}</td>
                                <td>{{ month_data.total_amount|floatformat:0|intcomma }} د.ع</td>
                                <td>{{ month_data.invoices_count|intcomma }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
function printReport() {
    window.print();
}
</script>

<style>
@media print {
    .btn, .card-header .btn, .no-print {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .table {
        border: 1px solid #000 !important;
    }
    
    .table th, .table td {
        border: 1px solid #000 !important;
    }
}
</style>
{% endblock %}
