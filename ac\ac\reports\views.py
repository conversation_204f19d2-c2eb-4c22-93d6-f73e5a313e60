from django.shortcuts import render
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse, JsonResponse
from django.db.models import Sum, Count, Q
from django.utils import timezone
from datetime import date, datetime, timedelta
from decimal import Decimal
import csv
import json
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
import xlsxwriter
from io import BytesIO

from accounts.models import Customer, Supplier
from invoices.models import SalesInvoice, PurchaseInvoice
from payments.models import Payment
from .models import ReportManager

class ReportBaseMixin:
    """مخلوط أساسي للتقارير لإضافة البيانات المشتركة"""

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # إضافة معلومات الشركة ورمز العملة
        from core.models import CompanySettings
        company = CompanySettings.objects.first()
        context['company'] = company
        context['base_currency_symbol'] = company.base_currency.symbol if (company and company.base_currency) else 'د.ع'
        return context

class ReportsIndexView(LoginRequiredMixin, TemplateView):
    """صفحة التقارير الرئيسية"""
    template_name = 'reports/index.html'

class CustomerBalancesReportView(LoginRequiredMixin, TemplateView):
    """تقرير أرصدة العملاء"""
    template_name = 'reports/customer_balances.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        as_of_date = self.request.GET.get('as_of_date')

        if as_of_date:
            try:
                as_of_date = date.fromisoformat(as_of_date)
            except ValueError:
                as_of_date = date.today()
        else:
            as_of_date = date.today()

        context['balances'] = ReportManager.get_customer_balances(as_of_date)
        context['as_of_date'] = as_of_date
        return context

class SupplierBalancesReportView(LoginRequiredMixin, TemplateView):
    """تقرير أرصدة الموردين"""
    template_name = 'reports/supplier_balances.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        as_of_date = self.request.GET.get('as_of_date')

        if as_of_date:
            try:
                as_of_date = date.fromisoformat(as_of_date)
            except ValueError:
                as_of_date = date.today()
        else:
            as_of_date = date.today()

        context['balances'] = ReportManager.get_supplier_balances(as_of_date)
        context['as_of_date'] = as_of_date
        return context

class CashFlowReportView(LoginRequiredMixin, TemplateView):
    """تقرير التدفق النقدي"""
    template_name = 'reports/cash_flow.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على التواريخ من الطلب أو استخدام القيم الافتراضية
        end_date = self.request.GET.get('end_date')
        start_date = self.request.GET.get('start_date')

        if end_date:
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()
        else:
            end_date = date.today()

        if start_date:
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=30)

        context.update(ReportManager.get_cash_flow_report(start_date, end_date))
        return context

class SalesSummaryReportView(LoginRequiredMixin, TemplateView):
    """تقرير ملخص المبيعات"""
    template_name = 'reports/sales_summary.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على التواريخ من الطلب أو استخدام القيم الافتراضية
        end_date = self.request.GET.get('end_date')
        start_date = self.request.GET.get('start_date')

        if end_date:
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()
        else:
            end_date = date.today()

        if start_date:
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=30)

        context.update(ReportManager.get_sales_summary(start_date, end_date))
        return context

class SalesSummaryPrintView(LoginRequiredMixin, ReportBaseMixin, TemplateView):
    """طباعة تقرير ملخص المبيعات"""
    template_name = 'reports/sales_summary_print.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على التواريخ من الطلب أو استخدام القيم الافتراضية
        end_date = self.request.GET.get('end_date')
        start_date = self.request.GET.get('start_date')

        if end_date:
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()
        else:
            end_date = date.today()

        if start_date:
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=30)

        # إضافة التاريخ الحالي
        context['current_date'] = timezone.now()
        context['start_date'] = start_date
        context['end_date'] = end_date

        # إضافة بيانات التقرير
        context.update(ReportManager.get_sales_summary(start_date, end_date))
        return context

class PurchaseSummaryPrintView(LoginRequiredMixin, ReportBaseMixin, TemplateView):
    """طباعة تقرير ملخص المشتريات"""
    template_name = 'reports/purchase_summary_print.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على التواريخ من الطلب أو استخدام القيم الافتراضية
        end_date = self.request.GET.get('end_date')
        start_date = self.request.GET.get('start_date')

        if end_date:
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()
        else:
            end_date = date.today()

        if start_date:
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=30)

        # إضافة التاريخ الحالي
        context['current_date'] = timezone.now()
        context['start_date'] = start_date
        context['end_date'] = end_date

        # إضافة بيانات التقرير
        context.update(ReportManager.get_purchase_summary(start_date, end_date))
        return context

class SupplierBalancesPrintView(LoginRequiredMixin, ReportBaseMixin, TemplateView):
    """طباعة تقرير أرصدة الموردين"""
    template_name = 'reports/supplier_balances_print.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على التاريخ من الطلب أو استخدام التاريخ الحالي
        as_of_date = self.request.GET.get('as_of_date')
        if as_of_date:
            try:
                as_of_date = date.fromisoformat(as_of_date)
            except ValueError:
                as_of_date = date.today()
        else:
            as_of_date = date.today()

        # إضافة التاريخ الحالي
        context['current_date'] = timezone.now()
        context['as_of_date'] = as_of_date

        # إضافة بيانات التقرير
        supplier_balances = ReportManager.get_supplier_balances(as_of_date)

        # حساب الإحصائيات
        total_suppliers = len(supplier_balances)
        total_payable = sum(balance['balance'] for balance in supplier_balances if balance['balance'] < 0)
        total_prepaid = sum(balance['balance'] for balance in supplier_balances if balance['balance'] > 0)
        total_purchases = sum(balance.get('total_purchases', 0) for balance in supplier_balances)
        total_payments = sum(balance.get('total_payments', 0) for balance in supplier_balances)
        net_balance = sum(balance['balance'] for balance in supplier_balances)

        context.update({
            'supplier_balances': supplier_balances,
            'total_suppliers': total_suppliers,
            'total_payable': abs(total_payable),
            'total_prepaid': total_prepaid,
            'total_purchases': total_purchases,
            'total_payments': total_payments,
            'net_balance': net_balance,
        })

        return context

class DetailedTaxReportView(LoginRequiredMixin, TemplateView):
    """تقرير الضرائب المفصل"""
    template_name = 'reports/detailed_tax.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على التواريخ
        end_date = self.request.GET.get('end_date')
        start_date = self.request.GET.get('start_date')

        if end_date:
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()
        else:
            end_date = date.today()

        if start_date:
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=30)

        context['start_date'] = start_date
        context['end_date'] = end_date
        context.update(ReportManager.get_detailed_tax_report(start_date, end_date))
        return context

class DetailedTaxPrintView(LoginRequiredMixin, ReportBaseMixin, TemplateView):
    """طباعة تقرير الضرائب المفصل"""
    template_name = 'reports/detailed_tax_print.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على التواريخ
        end_date = self.request.GET.get('end_date')
        start_date = self.request.GET.get('start_date')

        if end_date:
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()
        else:
            end_date = date.today()

        if start_date:
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=30)

        context['current_date'] = timezone.now()
        context['start_date'] = start_date
        context['end_date'] = end_date
        context.update(ReportManager.get_detailed_tax_report(start_date, end_date))
        return context

class ProfitLossPrintView(LoginRequiredMixin, ReportBaseMixin, TemplateView):
    """طباعة تقرير الأرباح والخسائر"""
    template_name = 'reports/profit_loss_print.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على التواريخ
        end_date = self.request.GET.get('end_date')
        start_date = self.request.GET.get('start_date')

        if end_date:
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()
        else:
            end_date = date.today()

        if start_date:
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=30)

        context['current_date'] = timezone.now()
        context['start_date'] = start_date
        context['end_date'] = end_date
        context.update(ReportManager.get_profit_loss_report(start_date, end_date))
        return context

class BalanceSheetReportView(LoginRequiredMixin, TemplateView):
    """تقرير الميزانية العمومية"""
    template_name = 'reports/balance_sheet.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على التاريخ
        as_of_date = self.request.GET.get('as_of_date')
        if as_of_date:
            try:
                as_of_date = date.fromisoformat(as_of_date)
            except ValueError:
                as_of_date = date.today()
        else:
            as_of_date = date.today()

        context['as_of_date'] = as_of_date
        context.update(ReportManager.get_balance_sheet_report(as_of_date))
        return context

class BalanceSheetPrintView(LoginRequiredMixin, ReportBaseMixin, TemplateView):
    """طباعة تقرير الميزانية العمومية"""
    template_name = 'reports/balance_sheet_print.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على التاريخ
        as_of_date = self.request.GET.get('as_of_date')
        if as_of_date:
            try:
                as_of_date = date.fromisoformat(as_of_date)
            except ValueError:
                as_of_date = date.today()
        else:
            as_of_date = date.today()

        context['current_date'] = timezone.now()
        context['as_of_date'] = as_of_date
        context.update(ReportManager.get_balance_sheet_report(as_of_date))
        return context

class CurrencyReportView(LoginRequiredMixin, TemplateView):
    """تقرير العملات"""
    template_name = 'reports/currency_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(ReportManager.get_currency_report())
        return context

class CurrencyReportPrintView(LoginRequiredMixin, ReportBaseMixin, TemplateView):
    """طباعة تقرير العملات"""
    template_name = 'reports/currency_report_print.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['current_date'] = timezone.now()
        context.update(ReportManager.get_currency_report())
        return context

class OverdueInvoicesReportView(LoginRequiredMixin, TemplateView):
    """تقرير الفواتير المتأخرة"""
    template_name = 'reports/overdue_invoices.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على التاريخ
        as_of_date = self.request.GET.get('as_of_date')
        if as_of_date:
            try:
                as_of_date = date.fromisoformat(as_of_date)
            except ValueError:
                as_of_date = date.today()
        else:
            as_of_date = date.today()

        context['as_of_date'] = as_of_date
        context.update(ReportManager.get_overdue_invoices_report(as_of_date))
        return context

class OverdueInvoicesPrintView(LoginRequiredMixin, ReportBaseMixin, TemplateView):
    """طباعة تقرير الفواتير المتأخرة"""
    template_name = 'reports/overdue_invoices_print.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على التاريخ
        as_of_date = self.request.GET.get('as_of_date')
        if as_of_date:
            try:
                as_of_date = date.fromisoformat(as_of_date)
            except ValueError:
                as_of_date = date.today()
        else:
            as_of_date = date.today()

        context['current_date'] = timezone.now()
        context['as_of_date'] = as_of_date
        context.update(ReportManager.get_overdue_invoices_report(as_of_date))
        return context

class CustomerBalancesPrintView(LoginRequiredMixin, ReportBaseMixin, TemplateView):
    """طباعة تقرير أرصدة العملاء"""
    template_name = 'reports/customer_balances_print.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على التاريخ
        as_of_date = self.request.GET.get('as_of_date')
        if as_of_date:
            try:
                as_of_date = date.fromisoformat(as_of_date)
            except ValueError:
                as_of_date = date.today()
        else:
            as_of_date = date.today()

        context['current_date'] = timezone.now()
        context['as_of_date'] = as_of_date

        # إضافة بيانات التقرير
        customer_balances = ReportManager.get_customer_balances(as_of_date)

        # حساب الإحصائيات
        total_customers = len(customer_balances)
        total_receivable = sum(balance['balance'] for balance in customer_balances if balance['balance'] > 0)
        total_prepaid = sum(abs(balance['balance']) for balance in customer_balances if balance['balance'] < 0)
        total_sales = sum(balance.get('total_sales', 0) for balance in customer_balances)
        total_payments = sum(balance.get('total_payments', 0) for balance in customer_balances)
        net_balance = sum(balance['balance'] for balance in customer_balances)

        context.update({
            'customer_balances': customer_balances,
            'total_customers': total_customers,
            'total_receivable': total_receivable,
            'total_prepaid': total_prepaid,
            'total_sales': total_sales,
            'total_payments': total_payments,
            'net_balance': net_balance,
        })

        return context

# ========== تصدير التقارير ==========
class CustomerBalancesExportView(LoginRequiredMixin, TemplateView):
    """تصدير تقرير أرصدة العملاء"""

    def get(self, request, *args, **kwargs):
        as_of_date = request.GET.get('as_of_date', date.today())
        if isinstance(as_of_date, str):
            try:
                as_of_date = date.fromisoformat(as_of_date)
            except ValueError:
                as_of_date = date.today()

        balances = ReportManager.get_customer_balances(as_of_date)

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="customer_balances_{as_of_date}.csv"'
        response.write('\ufeff')  # BOM for Arabic support

        writer = csv.writer(response)
        writer.writerow(['كود العميل', 'اسم العميل', 'الرصيد', 'المبلغ المستحق', 'عدد الفواتير'])

        for balance in balances:
            writer.writerow([
                balance['customer'].customer_code,
                balance['customer'].name,
                balance['balance'],
                balance['overdue_amount'],
                balance['invoices_count']
            ])

        return response

class SupplierBalancesExportView(LoginRequiredMixin, TemplateView):
    """تصدير تقرير أرصدة الموردين"""

    def get(self, request, *args, **kwargs):
        as_of_date = request.GET.get('as_of_date', date.today())
        if isinstance(as_of_date, str):
            try:
                as_of_date = date.fromisoformat(as_of_date)
            except ValueError:
                as_of_date = date.today()

        balances = ReportManager.get_supplier_balances(as_of_date)

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="supplier_balances_{as_of_date}.csv"'
        response.write('\ufeff')  # BOM for Arabic support

        writer = csv.writer(response)
        writer.writerow(['كود المورد', 'اسم المورد', 'الرصيد', 'المبلغ المستحق', 'عدد الفواتير'])

        for balance in balances:
            writer.writerow([
                balance['supplier'].supplier_code,
                balance['supplier'].name,
                balance['balance'],
                balance['overdue_amount'],
                balance['invoices_count']
            ])

        return response

class CashFlowExportView(LoginRequiredMixin, TemplateView):
    """تصدير تقرير التدفق النقدي"""

    def get(self, request, *args, **kwargs):
        end_date = request.GET.get('end_date', date.today())
        start_date = request.GET.get('start_date', date.today() - timedelta(days=30))

        if isinstance(end_date, str):
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()

        if isinstance(start_date, str):
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)

        cash_flow = ReportManager.get_cash_flow_report(start_date, end_date)

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="cash_flow_{start_date}_{end_date}.csv"'
        response.write('\ufeff')  # BOM for Arabic support

        writer = csv.writer(response)
        writer.writerow(['التدفق النقدي', f'من {start_date} إلى {end_date}'])
        writer.writerow(['التدفق الداخل', cash_flow['cash_in']])
        writer.writerow(['التدفق الخارج', cash_flow['cash_out']])
        writer.writerow(['صافي التدفق', cash_flow['net_cash_flow']])

        return response

class SalesSummaryExportView(LoginRequiredMixin, TemplateView):
    """تصدير تقرير ملخص المبيعات"""

    def get(self, request, *args, **kwargs):
        end_date = request.GET.get('end_date', date.today())
        start_date = request.GET.get('start_date', date.today() - timedelta(days=30))

        if isinstance(end_date, str):
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()

        if isinstance(start_date, str):
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)

        sales_summary = ReportManager.get_sales_summary(start_date, end_date)

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="sales_summary_{start_date}_{end_date}.csv"'
        response.write('\ufeff')  # BOM for Arabic support

        writer = csv.writer(response)
        writer.writerow(['ملخص المبيعات', f'من {start_date} إلى {end_date}'])
        writer.writerow(['إجمالي المبيعات', sales_summary['total_sales']])
        writer.writerow(['إجمالي الضرائب', sales_summary['total_tax']])
        writer.writerow(['عدد الفواتير', sales_summary['invoices_count']])

        return response

# ========== Excel Export Views ==========

class ExcelReportMixin:
    """مخلوط لتصدير التقارير إلى Excel"""

    def create_excel_workbook(self, title="تقرير"):
        """إنشاء مصنف Excel مع التنسيق الأساسي"""
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})

        # تنسيقات مختلفة
        formats = {
            'title': workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter',
                'bg_color': '#1e7e34',
                'font_color': 'white'
            }),
            'header': workbook.add_format({
                'bold': True,
                'font_size': 12,
                'align': 'center',
                'valign': 'vcenter',
                'bg_color': '#28a745',
                'font_color': 'white',
                'border': 1
            }),
            'data': workbook.add_format({
                'font_size': 11,
                'align': 'right',
                'valign': 'vcenter',
                'border': 1
            }),
            'number': workbook.add_format({
                'font_size': 11,
                'align': 'right',
                'valign': 'vcenter',
                'border': 1,
                'num_format': '#,##0.00'
            }),
            'total': workbook.add_format({
                'bold': True,
                'font_size': 12,
                'align': 'right',
                'valign': 'vcenter',
                'bg_color': '#ffc107',
                'border': 1,
                'num_format': '#,##0.00'
            })
        }

        return workbook, output, formats

class CustomerBalancesExcelView(LoginRequiredMixin, ExcelReportMixin, TemplateView):
    """تصدير تقرير أرصدة العملاء إلى Excel"""

    def get(self, request, *args, **kwargs):
        as_of_date = request.GET.get('as_of_date')
        if as_of_date:
            try:
                as_of_date = date.fromisoformat(as_of_date)
            except ValueError:
                as_of_date = date.today()
        else:
            as_of_date = date.today()

        balances = ReportManager.get_customer_balances(as_of_date)

        # إنشاء ملف Excel
        workbook, output, formats = self.create_excel_workbook()
        worksheet = workbook.add_worksheet('أرصدة العملاء')

        # عنوان التقرير
        worksheet.merge_range('A1:F1', f'تقرير أرصدة العملاء كما في {as_of_date}', formats['title'])
        worksheet.set_row(0, 25)

        # رؤوس الأعمدة
        headers = ['كود العميل', 'اسم العميل', 'الرصيد', 'المبلغ المتأخر', 'عدد الفواتير', 'حالة الحساب']
        for col, header in enumerate(headers):
            worksheet.write(2, col, header, formats['header'])
            worksheet.set_column(col, col, 20)

        # البيانات
        row = 3
        total_balance = Decimal('0.00')
        total_overdue = Decimal('0.00')
        total_invoices_count = 0

        for balance in balances:
            worksheet.write(row, 0, balance['customer'].customer_code, formats['data'])
            worksheet.write(row, 1, balance['customer'].name, formats['data'])
            worksheet.write(row, 2, float(balance['balance']), formats['number'])
            worksheet.write(row, 3, float(balance['overdue_amount']), formats['number'])
            worksheet.write(row, 4, balance['invoices_count'], formats['data'])

            status = 'دائن' if balance['balance'] > 0 else 'مدين' if balance['balance'] < 0 else 'متوازن'
            worksheet.write(row, 5, status, formats['data'])

            total_balance += balance['balance']
            total_overdue += balance['overdue_amount']
            total_invoices_count += balance['invoices_count']
            row += 1

        # إجمالي
        worksheet.write(row + 1, 1, 'الإجمالي', formats['total'])
        worksheet.write(row + 1, 2, float(total_balance), formats['total'])
        worksheet.write(row + 1, 3, float(total_overdue), formats['total'])
        worksheet.write(row + 1, 4, total_invoices_count, formats['total'])

        workbook.close()
        output.seek(0)

        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="customer_balances_{as_of_date}.xlsx"'
        return response

class SalesSummaryExcelView(LoginRequiredMixin, ExcelReportMixin, TemplateView):
    """تصدير تقرير ملخص المبيعات إلى Excel"""

    def get(self, request, *args, **kwargs):
        end_date = request.GET.get('end_date')
        start_date = request.GET.get('start_date')

        if end_date:
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()
        else:
            end_date = date.today()

        if start_date:
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=30)

        sales_data = ReportManager.get_sales_summary(start_date, end_date)

        # إنشاء ملف Excel
        workbook, output, formats = self.create_excel_workbook()
        worksheet = workbook.add_worksheet('ملخص المبيعات')

        # عنوان التقرير
        worksheet.merge_range('A1:E1', f'تقرير ملخص المبيعات من {start_date} إلى {end_date}', formats['title'])
        worksheet.set_row(0, 25)

        # الملخص العام
        worksheet.write('A3', 'إجمالي المبيعات:', formats['header'])
        worksheet.write('B3', float(sales_data['total_sales']), formats['number'])
        worksheet.write('A4', 'إجمالي الضرائب:', formats['header'])
        worksheet.write('B4', float(sales_data['total_tax']), formats['number'])
        worksheet.write('A5', 'عدد الفواتير:', formats['header'])
        worksheet.write('B5', sales_data['invoices_count'], formats['data'])

        # المبيعات حسب العملاء
        worksheet.write('A7', 'المبيعات حسب العملاء:', formats['title'])
        headers = ['اسم العميل', 'إجمالي المبيعات', 'عدد الفواتير']
        for col, header in enumerate(headers):
            worksheet.write(8, col, header, formats['header'])
            worksheet.set_column(col, col, 25)

        row = 9
        for sale in sales_data['sales_by_customer']:
            worksheet.write(row, 0, sale['customer__name'], formats['data'])
            worksheet.write(row, 1, float(sale['total_amount']), formats['number'])
            worksheet.write(row, 2, sale['invoices_count'], formats['data'])
            row += 1

        workbook.close()
        output.seek(0)

        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="sales_summary_{start_date}_{end_date}.xlsx"'
        return response

# ========== New Report Views ==========

class PurchaseSummaryReportView(LoginRequiredMixin, TemplateView):
    """تقرير ملخص المشتريات"""
    template_name = 'reports/purchase_summary.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        end_date = self.request.GET.get('end_date')
        start_date = self.request.GET.get('start_date')

        if end_date:
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()
        else:
            end_date = date.today()

        if start_date:
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=30)

        context.update(ReportManager.get_purchase_summary(start_date, end_date))
        return context

class TaxReportView(LoginRequiredMixin, TemplateView):
    """تقرير الضرائب"""
    template_name = 'reports/tax_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        end_date = self.request.GET.get('end_date')
        start_date = self.request.GET.get('start_date')

        if end_date:
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()
        else:
            end_date = date.today()

        if start_date:
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=30)

        context.update(ReportManager.get_tax_report(start_date, end_date))
        return context

class ProfitLossReportView(LoginRequiredMixin, TemplateView):
    """تقرير الأرباح والخسائر"""
    template_name = 'reports/profit_loss.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        end_date = self.request.GET.get('end_date')
        start_date = self.request.GET.get('start_date')

        if end_date:
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()
        else:
            end_date = date.today()

        if start_date:
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=30)

        context.update(ReportManager.get_profit_loss_report(start_date, end_date))
        return context

class BalanceSheetReportView(LoginRequiredMixin, TemplateView):
    """تقرير الميزانية العمومية"""
    template_name = 'reports/balance_sheet.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        as_of_date = self.request.GET.get('as_of_date')

        if as_of_date:
            try:
                as_of_date = date.fromisoformat(as_of_date)
            except ValueError:
                as_of_date = date.today()
        else:
            as_of_date = date.today()

        context.update(ReportManager.get_balance_sheet_report(as_of_date))
        return context

# ========== Excel Export Views for New Reports ==========

class PurchaseSummaryExcelView(LoginRequiredMixin, ExcelReportMixin, TemplateView):
    """تصدير تقرير ملخص المشتريات إلى Excel"""

    def get(self, request, *args, **kwargs):
        end_date = request.GET.get('end_date')
        start_date = request.GET.get('start_date')

        if end_date:
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()
        else:
            end_date = date.today()

        if start_date:
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=30)

        purchase_data = ReportManager.get_purchase_summary(start_date, end_date)

        # إنشاء ملف Excel
        workbook, output, formats = self.create_excel_workbook()
        worksheet = workbook.add_worksheet('ملخص المشتريات')

        # عنوان التقرير
        worksheet.merge_range('A1:E1', f'تقرير ملخص المشتريات من {start_date} إلى {end_date}', formats['title'])
        worksheet.set_row(0, 25)

        # الملخص العام
        worksheet.write('A3', 'إجمالي المشتريات:', formats['header'])
        worksheet.write('B3', float(purchase_data['total_purchases']), formats['number'])
        worksheet.write('A4', 'إجمالي الضرائب:', formats['header'])
        worksheet.write('B4', float(purchase_data['total_tax']), formats['number'])
        worksheet.write('A5', 'عدد الفواتير:', formats['header'])
        worksheet.write('B5', purchase_data['invoices_count'], formats['data'])

        # المشتريات حسب الموردين
        worksheet.write('A7', 'المشتريات حسب الموردين:', formats['title'])
        headers = ['اسم المورد', 'إجمالي المشتريات', 'عدد الفواتير']
        for col, header in enumerate(headers):
            worksheet.write(8, col, header, formats['header'])
            worksheet.set_column(col, col, 25)

        row = 9
        for purchase in purchase_data['purchases_by_supplier']:
            worksheet.write(row, 0, purchase['supplier__name'], formats['data'])
            worksheet.write(row, 1, float(purchase['total_amount']), formats['number'])
            worksheet.write(row, 2, purchase['invoices_count'], formats['data'])
            row += 1

        workbook.close()
        output.seek(0)

        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="purchase_summary_{start_date}_{end_date}.xlsx"'
        return response

class TaxReportExcelView(LoginRequiredMixin, ExcelReportMixin, TemplateView):
    """تصدير تقرير الضرائب إلى Excel"""

    def get(self, request, *args, **kwargs):
        end_date = request.GET.get('end_date')
        start_date = request.GET.get('start_date')

        if end_date:
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()
        else:
            end_date = date.today()

        if start_date:
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=30)

        tax_data = ReportManager.get_tax_report(start_date, end_date)

        # إنشاء ملف Excel
        workbook, output, formats = self.create_excel_workbook()
        worksheet = workbook.add_worksheet('تقرير الضرائب')

        # عنوان التقرير
        worksheet.merge_range('A1:C1', f'تقرير الضرائب من {start_date} إلى {end_date}', formats['title'])
        worksheet.set_row(0, 25)

        # بيانات الضرائب
        worksheet.write('A3', 'ضرائب المبيعات:', formats['header'])
        worksheet.write('B3', float(tax_data['sales_tax']), formats['number'])

        worksheet.write('A4', 'ضرائب المشتريات:', formats['header'])
        worksheet.write('B4', float(tax_data['purchase_tax']), formats['number'])

        worksheet.write('A5', 'صافي الضريبة المستحقة:', formats['header'])
        worksheet.write('B5', float(tax_data['net_tax_due']), formats['total'])

        worksheet.write('A7', 'إجمالي المبيعات قبل الضريبة:', formats['header'])
        worksheet.write('B7', float(tax_data['sales_before_tax']), formats['number'])

        worksheet.write('A8', 'إجمالي المشتريات قبل الضريبة:', formats['header'])
        worksheet.write('B8', float(tax_data['purchase_before_tax']), formats['number'])

        workbook.close()
        output.seek(0)

        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="tax_report_{start_date}_{end_date}.xlsx"'
        return response


# ========== Custom Reports Views ==========

class OverdueInvoicesReportView(LoginRequiredMixin, TemplateView):
    """تقرير الفواتير المتأخرة"""
    template_name = 'reports/overdue_invoices.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        as_of_date = self.request.GET.get('as_of_date')

        if as_of_date:
            try:
                as_of_date = date.fromisoformat(as_of_date)
            except ValueError:
                as_of_date = date.today()
        else:
            as_of_date = date.today()

        context.update(ReportManager.get_overdue_invoices_report(as_of_date))
        return context


class CurrencyReportView(LoginRequiredMixin, TemplateView):
    """تقرير العملات وأسعار الصرف"""
    template_name = 'reports/currency_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(ReportManager.get_currency_report())
        return context


class DetailedTaxReportView(LoginRequiredMixin, TemplateView):
    """تقرير الضرائب المفصل"""
    template_name = 'reports/detailed_tax_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        end_date = self.request.GET.get('end_date')
        start_date = self.request.GET.get('start_date')

        if end_date:
            try:
                end_date = date.fromisoformat(end_date)
            except ValueError:
                end_date = date.today()
        else:
            end_date = date.today()

        if start_date:
            try:
                start_date = date.fromisoformat(start_date)
            except ValueError:
                start_date = end_date.replace(day=1)  # بداية الشهر الحالي
        else:
            start_date = end_date.replace(day=1)

        context.update(ReportManager.get_detailed_tax_report(start_date, end_date))
        return context
