{% extends 'base/base.html' %}

{% block title %}تقرير العملات - النظام المحاسبي{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">
                <i class="fas fa-coins text-warning me-2"></i>
                تقرير العملات وأسعار الصرف
            </h1>
            <a href="{% url 'reports:index' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
            </a>
        </div>
    </div>
</div>

<!-- ملخص العملات -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h5 class="card-title">العملة الأساسية</h5>
                <h3>{{ report_data.base_currency.code }}</h3>
                <small>{{ report_data.base_currency.name }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h5 class="card-title">إجمالي العملات</h5>
                <h3>{{ report_data.total_currencies }}</h3>
                <small>عملة مسجلة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h5 class="card-title">العملات النشطة</h5>
                <h3>{{ report_data.active_currencies }}</h3>
                <small>عملة نشطة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h5 class="card-title">تاريخ التقرير</h5>
                <h3>{{ "now"|date:'d/m' }}</h3>
                <small>{{ "now"|date:'Y' }}</small>
            </div>
        </div>
    </div>
</div>

<!-- جدول العملات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    تفاصيل العملات وأسعار الصرف
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>الرمز</th>
                                <th>اسم العملة</th>
                                <th>الرمز المختصر</th>
                                <th>سعر الصرف</th>
                                <th>آخر تحديث</th>
                                <th>التغيير</th>
                                <th>الحالة</th>
                                <th>نوع العملة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for currency_data in report_data.currencies %}
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ currency_data.currency.code }}</strong>
                                </td>
                                <td>{{ currency_data.currency.name }}</td>
                                <td>
                                    <span class="badge bg-secondary">{{ currency_data.currency.symbol }}</span>
                                </td>
                                <td class="text-end">
                                    {% if currency_data.currency.is_base_currency %}
                                        <span class="badge bg-primary">1.00 (أساسية)</span>
                                    {% else %}
                                        {{ currency_data.currency.exchange_rate|floatformat:4 }}
                                    {% endif %}
                                </td>
                                <td>{{ currency_data.last_update|date:'d/m/Y' }}</td>
                                <td class="text-center">
                                    {% if currency_data.rate_change > 0 %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-arrow-up me-1"></i>
                                            +{{ currency_data.rate_change|floatformat:4 }}
                                        </span>
                                    {% elif currency_data.rate_change < 0 %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-arrow-down me-1"></i>
                                            {{ currency_data.rate_change|floatformat:4 }}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-minus me-1"></i>
                                            لا تغيير
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if currency_data.currency.is_active %}
                                        <span class="badge bg-success">نشطة</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشطة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if currency_data.currency.is_base_currency %}
                                        <span class="badge bg-primary">
                                            <i class="fas fa-star me-1"></i>أساسية
                                        </span>
                                    {% else %}
                                        <span class="badge bg-info">ثانوية</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h5 class="alert-heading">
                <i class="fas fa-info-circle me-2"></i>
                معلومات مهمة حول العملات
            </h5>
            <hr>
            <p class="mb-2">
                <strong>العملة الأساسية:</strong> {{ report_data.base_currency.name }} ({{ report_data.base_currency.code }}) - سعر الصرف دائماً 1.00
            </p>
            <p class="mb-2">
                <strong>أسعار الصرف:</strong> تُحدث أسعار الصرف يدوياً من خلال إعدادات العملات
            </p>
            <p class="mb-2">
                <strong>العملات النشطة:</strong> فقط العملات النشطة تظهر في النماذج والتقارير
            </p>
            <p class="mb-0">
                <strong>التحويل:</strong> جميع المبالغ تُحول تلقائياً إلى العملة الأساسية في التقارير المالية
            </p>
        </div>
    </div>
</div>

<!-- أزرار الإجراءات -->
<div class="row mt-3">
    <div class="col-12 text-center">
        <a href="{% url 'core:currency_list' %}" class="btn btn-primary me-2">
            <i class="fas fa-cog me-2"></i>إدارة العملات
        </a>
        <a href="{% url 'reports:currency_report_print' %}"
           class="btn btn-success" target="_blank">
            <i class="fas fa-print me-2"></i>طباعة التقرير
        </a>
    </div>
</div>

{% endblock %}
