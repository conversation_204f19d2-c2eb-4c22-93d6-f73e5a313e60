{% extends 'base/base.html' %}

{% block title %}
    {% if object %}تحديث العملة{% else %}إضافة عملة جديدة{% endif %} - النظام المحاسبي
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                {% if object %}
                    <i class="fas fa-edit text-warning"></i>
                    تحديث العملة: {{ object.name }}
                {% else %}
                    <i class="fas fa-plus text-primary"></i>
                    إضافة عملة جديدة
                {% endif %}
            </h1>
            <div>
                <a href="{% url 'core:currency_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<form method="post" class="needs-validation" novalidate>
    {% csrf_token %}
    
    <div class="row">
        <!-- معلومات العملة -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات العملة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.code.id_for_label }}" class="form-label">
                                {{ form.code.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.code }}
                            {% if form.code.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.code.errors.0 }}
                                </div>
                            {% endif %}
                            {% if form.code.help_text %}
                                <div class="form-text">{{ form.code.help_text }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.exchange_rate.id_for_label }}" class="form-label">
                                {{ form.exchange_rate.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.exchange_rate }}
                            {% if form.exchange_rate.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.exchange_rate.errors.0 }}
                                </div>
                            {% endif %}
                            {% if form.exchange_rate.help_text %}
                                <div class="form-text">{{ form.exchange_rate.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                {{ form.is_base_currency }}
                                <label class="form-check-label" for="{{ form.is_base_currency.id_for_label }}">
                                    {{ form.is_base_currency.label }}
                                </label>
                                {% if form.is_base_currency.help_text %}
                                    <div class="form-text">{{ form.is_base_currency.help_text }}</div>
                                {% endif %}
                            </div>
                            {% if form.is_base_currency.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.is_base_currency.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {{ form.is_active.label }}
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.is_active.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- عرض الأخطاء العامة -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- معاينة العملة -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>معاينة العملة
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div id="currency-preview">
                        {% if object %}
                            <i class="fas fa-coins fa-3x text-warning mb-3"></i>
                            <h4 class="text-primary">{{ object.name }}</h4>
                            <p class="text-muted">{{ object.code }}</p>
                            <div class="badge bg-secondary fs-6 mb-2">{{ object.symbol }}</div>
                            <div class="mt-3">
                                <small class="text-muted">سعر الصرف الحالي</small>
                                <h5 class="text-success">{{ object.exchange_rate|floatformat:6 }}</h5>
                            </div>
                        {% else %}
                            <i class="fas fa-coins fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">اختر العملة</h4>
                            <p class="text-muted">سيتم عرض التفاصيل هنا</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- نصائح -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>نصائح
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-2"></i>اختر العملة من القائمة المحددة</li>
                        <li><i class="fas fa-check text-success me-2"></i>أدخل سعر الصرف بدقة</li>
                        <li><i class="fas fa-check text-success me-2"></i>يمكن تحديد عملة أساسية واحدة فقط</li>
                        <li><i class="fas fa-check text-success me-2"></i>سعر صرف العملة الأساسية = 1</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أزرار الحفظ -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>
                        {% if object %}تحديث العملة{% else %}حفظ العملة{% endif %}
                    </button>
                    <a href="{% url 'core:currency_list' %}" class="btn btn-secondary btn-lg ms-3">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const codeField = document.querySelector('#id_code');
    const exchangeRateField = document.querySelector('#id_exchange_rate');
    const isBaseCurrencyField = document.querySelector('#id_is_base_currency');
    const preview = document.querySelector('#currency-preview');
    
    // معلومات العملات
    const currencyInfo = {
        'IQD': { name: 'الدينار العراقي', symbol: 'د.ع' },
        'USD': { name: 'الدولار الأمريكي', symbol: '$' },
        'AED': { name: 'الدرهم الإماراتي', symbol: 'د.إ' },
        'QAR': { name: 'الريال القطري', symbol: 'ر.ق' },
        'EUR': { name: 'اليورو', symbol: '€' }
    };
    
    // تحديث المعاينة عند تغيير العملة
    function updatePreview() {
        const selectedCode = codeField?.value;
        const exchangeRate = exchangeRateField?.value || '1.000000';
        const isBase = isBaseCurrencyField?.checked;
        
        if (selectedCode && currencyInfo[selectedCode]) {
            const info = currencyInfo[selectedCode];
            preview.innerHTML = `
                <i class="fas fa-coins fa-3x text-warning mb-3"></i>
                <h4 class="text-primary">${info.name}</h4>
                <p class="text-muted">${selectedCode}</p>
                <div class="badge bg-secondary fs-6 mb-2">${info.symbol}</div>
                <div class="mt-3">
                    <small class="text-muted">سعر الصرف</small>
                    <h5 class="${isBase ? 'text-success' : 'text-warning'}">${isBase ? '1.000000' : exchangeRate}</h5>
                    ${isBase ? '<small class="text-success">عملة أساسية</small>' : ''}
                </div>
            `;
        }
    }
    
    // ربط الأحداث
    codeField?.addEventListener('change', updatePreview);
    exchangeRateField?.addEventListener('input', updatePreview);
    isBaseCurrencyField?.addEventListener('change', function() {
        if (this.checked) {
            exchangeRateField.value = '1.000000';
            exchangeRateField.readOnly = true;
        } else {
            exchangeRateField.readOnly = false;
        }
        updatePreview();
    });
    
    // تحديث المعاينة عند تحميل الصفحة
    updatePreview();
    
    // التحقق من العملة الأساسية
    if (isBaseCurrencyField?.checked) {
        exchangeRateField.readOnly = true;
    }
    
    // إضافة مؤشر التحميل عند الحفظ
    document.querySelector('form').addEventListener('submit', function() {
        const submitBtn = document.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
        submitBtn.disabled = true;
    });
});
</script>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

#currency-preview {
    transition: all 0.3s ease;
}

.badge {
    font-size: 1em !important;
}
</style>
{% endblock %}
