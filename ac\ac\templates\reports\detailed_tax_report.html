{% extends 'base/base.html' %}

{% block title %}تقرير الضرائب المفصل - النظام المحاسبي{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">
                <i class="fas fa-percentage text-dark me-2"></i>
                تقرير الضرائب المفصل
            </h1>
            <a href="{% url 'reports:index' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
            </a>
        </div>
    </div>
</div>

<!-- فلاتر التقرير -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>فلاتر التقرير
                </h5>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="start_date" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="{{ start_date|date:'Y-m-d' }}">
                    </div>
                    <div class="col-md-4">
                        <label for="end_date" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="{{ end_date|date:'Y-m-d' }}">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>عرض التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- ملخص الضرائب -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h5 class="card-title">ضرائب محصلة</h5>
                <h3>{{ report_data.total_tax_collected|floatformat:2 }}</h3>
                <small>{{ base_currency_symbol }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h5 class="card-title">ضرائب مدفوعة</h5>
                <h3>{{ report_data.total_tax_paid|floatformat:2 }}</h3>
                <small>{{ base_currency_symbol }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h5 class="card-title">صافي الضريبة</h5>
                <h3>{{ report_data.net_tax_due|floatformat:2 }}</h3>
                <small>{{ base_currency_symbol }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h5 class="card-title">الفترة</h5>
                <h3>{{ start_date|date:'d/m' }} - {{ end_date|date:'d/m' }}</h3>
                <small>{{ start_date|date:'Y' }}</small>
            </div>
        </div>
    </div>
</div>

<!-- تفاصيل الضرائب حسب النوع -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    تفاصيل الضرائب حسب النوع
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>نوع الضريبة</th>
                                <th>المعدل</th>
                                <th>ضرائب المبيعات</th>
                                <th>عدد فواتير البيع</th>
                                <th>ضرائب المشتريات</th>
                                <th>عدد فواتير الشراء</th>
                                <th>صافي الضريبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for tax_detail in report_data.tax_details %}
                            <tr>
                                <td>
                                    <strong>{{ tax_detail.tax_type.name }}</strong>
                                    {% if tax_detail.tax_type.is_default %}
                                        <span class="badge bg-primary ms-2">افتراضية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if tax_detail.tax_type.calculation_type == 'percentage' %}
                                        {{ tax_detail.tax_type.rate }}%
                                    {% elif tax_detail.tax_type.calculation_type == 'fixed' %}
                                        {{ tax_detail.tax_type.rate }} {{ base_currency_symbol }}
                                    {% else %}
                                        {{ tax_detail.tax_type.rate }}% (مركبة)
                                    {% endif %}
                                </td>
                                <td class="text-end text-success">
                                    {{ tax_detail.sales_tax|floatformat:2 }} {{ base_currency_symbol }}
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-success">{{ tax_detail.sales_count }}</span>
                                </td>
                                <td class="text-end text-warning">
                                    {{ tax_detail.purchase_tax|floatformat:2 }} {{ base_currency_symbol }}
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-warning">{{ tax_detail.purchase_count }}</span>
                                </td>
                                <td class="text-end">
                                    {% if tax_detail.net_tax > 0 %}
                                        <span class="text-success fw-bold">
                                            +{{ tax_detail.net_tax|floatformat:2 }} {{ base_currency_symbol }}
                                        </span>
                                    {% elif tax_detail.net_tax < 0 %}
                                        <span class="text-danger fw-bold">
                                            {{ tax_detail.net_tax|floatformat:2 }} {{ base_currency_symbol }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">
                                            0.00 {{ base_currency_symbol }}
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="text-center text-muted">
                                    <i class="fas fa-info-circle me-2"></i>
                                    لا توجد بيانات ضريبية في الفترة المحددة
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        {% if report_data.tax_details %}
                        <tfoot class="table-secondary">
                            <tr>
                                <th colspan="2">الإجمالي</th>
                                <th class="text-end">{{ report_data.total_tax_collected|floatformat:2 }} {{ base_currency_symbol }}</th>
                                <th class="text-center">
                                    <span class="badge bg-success">
                                        {% widthratio report_data.tax_details|length 1 1 %}
                                    </span>
                                </th>
                                <th class="text-end">{{ report_data.total_tax_paid|floatformat:2 }} {{ base_currency_symbol }}</th>
                                <th class="text-center">
                                    <span class="badge bg-warning">
                                        {% widthratio report_data.tax_details|length 1 1 %}
                                    </span>
                                </th>
                                <th class="text-end">
                                    {% if report_data.net_tax_due > 0 %}
                                        <span class="text-success fw-bold">
                                            +{{ report_data.net_tax_due|floatformat:2 }} {{ base_currency_symbol }}
                                        </span>
                                    {% elif report_data.net_tax_due < 0 %}
                                        <span class="text-danger fw-bold">
                                            {{ report_data.net_tax_due|floatformat:2 }} {{ base_currency_symbol }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">
                                            0.00 {{ base_currency_symbol }}
                                        </span>
                                    {% endif %}
                                </th>
                            </tr>
                        </tfoot>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h5 class="alert-heading">
                <i class="fas fa-info-circle me-2"></i>
                تفسير التقرير
            </h5>
            <hr>
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-2">
                        <strong>ضرائب محصلة:</strong> الضرائب المحصلة من العملاء على فواتير البيع
                    </p>
                    <p class="mb-2">
                        <strong>ضرائب مدفوعة:</strong> الضرائب المدفوعة للموردين على فواتير الشراء
                    </p>
                </div>
                <div class="col-md-6">
                    <p class="mb-2">
                        <strong>صافي الضريبة:</strong> الفرق بين الضرائب المحصلة والمدفوعة
                    </p>
                    <p class="mb-0">
                        <strong>القيمة الموجبة:</strong> مبلغ مستحق للحكومة | <strong>القيمة السالبة:</strong> مبلغ مستحق من الحكومة
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أزرار الإجراءات -->
<div class="row mt-3">
    <div class="col-12 text-center">
        <a href="{% url 'core:tax_list' %}" class="btn btn-primary me-2">
            <i class="fas fa-cog me-2"></i>إدارة الضرائب
        </a>
        <a href="{% url 'reports:detailed_tax_print' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
           class="btn btn-success" target="_blank">
            <i class="fas fa-print me-2"></i>طباعة التقرير
        </a>
    </div>
</div>

{% endblock %}
