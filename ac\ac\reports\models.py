from django.db import models
from django.db.models import Sum, Q, Count
from decimal import Decimal
from datetime import date, datetime

class ReportManager:
    """مدير التقارير المالية"""

    @staticmethod
    def get_customer_balances(as_of_date=None):
        """تقرير أرصدة العملاء"""
        from accounts.models import Customer
        from invoices.models import SalesInvoice
        from payments.models import Payment

        if not as_of_date:
            as_of_date = date.today()

        customers = Customer.objects.filter(is_active=True)
        balances = []

        for customer in customers:
            # إجمالي الفواتير
            invoices_total = SalesInvoice.objects.filter(
                customer=customer,
                invoice_date__lte=as_of_date,
                status__in=['pending', 'partial', 'paid']
            ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0.00')

            # إجمالي المدفوعات
            payments_total = Payment.objects.filter(
                customer=customer,
                payment_date__lte=as_of_date,
                payment_type='received'
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

            balance = invoices_total - payments_total

            if balance != 0:  # عرض العملاء الذين لديهم رصيد فقط
                # الفواتير المستحقة
                overdue_invoices = SalesInvoice.objects.filter(
                    customer=customer,
                    due_date__lt=as_of_date,
                    status__in=['pending', 'partial']
                ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0.00')

                balances.append({
                    'customer': customer,
                    'balance': balance,
                    'overdue_amount': overdue_invoices,
                    'invoices_count': SalesInvoice.objects.filter(
                        customer=customer, status__in=['pending', 'partial']
                    ).count()
                })

        return sorted(balances, key=lambda x: x['balance'], reverse=True)

    @staticmethod
    def get_supplier_balances(as_of_date=None):
        """تقرير أرصدة الموردين"""
        from accounts.models import Supplier
        from invoices.models import PurchaseInvoice
        from payments.models import Payment

        if not as_of_date:
            as_of_date = date.today()

        suppliers = Supplier.objects.filter(is_active=True)
        balances = []

        for supplier in suppliers:
            # إجمالي الفواتير
            invoices_total = PurchaseInvoice.objects.filter(
                supplier=supplier,
                invoice_date__lte=as_of_date,
                status__in=['pending', 'partial', 'paid']
            ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0.00')

            # إجمالي المدفوعات
            payments_total = Payment.objects.filter(
                supplier=supplier,
                payment_date__lte=as_of_date,
                payment_type='paid'
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

            balance = invoices_total - payments_total

            if balance != 0:  # عرض الموردين الذين لديهم رصيد فقط
                # الفواتير المستحقة
                overdue_invoices = PurchaseInvoice.objects.filter(
                    supplier=supplier,
                    due_date__lt=as_of_date,
                    status__in=['pending', 'partial']
                ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0.00')

                balances.append({
                    'supplier': supplier,
                    'balance': balance,
                    'overdue_amount': overdue_invoices,
                    'invoices_count': PurchaseInvoice.objects.filter(
                        supplier=supplier, status__in=['pending', 'partial']
                    ).count()
                })

        return sorted(balances, key=lambda x: x['balance'], reverse=True)

    @staticmethod
    def get_cash_flow_report(start_date, end_date):
        """تقرير التدفق النقدي"""
        from payments.models import Payment

        # المدفوعات المستلمة (التدفق الداخل)
        cash_in = Payment.objects.filter(
            payment_type='received',
            payment_date__range=[start_date, end_date]
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        # المدفوعات المسددة (التدفق الخارج)
        cash_out = Payment.objects.filter(
            payment_type='paid',
            payment_date__range=[start_date, end_date]
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        # تفاصيل المدفوعات المستلمة
        received_payments = Payment.objects.filter(
            payment_type='received',
            payment_date__range=[start_date, end_date]
        ).select_related('customer').order_by('-payment_date')

        # تفاصيل المدفوعات المسددة
        paid_payments = Payment.objects.filter(
            payment_type='paid',
            payment_date__range=[start_date, end_date]
        ).select_related('supplier').order_by('-payment_date')

        return {
            'cash_in': cash_in,
            'cash_out': cash_out,
            'net_cash_flow': cash_in - cash_out,
            'received_payments': received_payments,
            'paid_payments': paid_payments,
            'start_date': start_date,
            'end_date': end_date
        }

    @staticmethod
    def get_sales_summary(start_date, end_date):
        """تقرير ملخص المبيعات"""
        from invoices.models import SalesInvoice

        # إجمالي المبيعات
        total_sales = SalesInvoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['pending', 'partial', 'paid']
        ).aggregate(
            total_amount=Sum('total_amount'),
            total_tax=Sum('tax_amount'),
            count=Count('id')
        )

        # المبيعات حسب العملاء
        sales_by_customer = SalesInvoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['pending', 'partial', 'paid']
        ).values('customer__name').annotate(
            total_amount=Sum('total_amount'),
            invoices_count=Count('id')
        ).order_by('-total_amount')

        # المبيعات حسب الشهر (إذا كان النطاق أكثر من شهر)
        sales_by_month = SalesInvoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['pending', 'partial', 'paid']
        ).extra(
            select={'month': "strftime('%%Y-%%m', invoice_date)"}
        ).values('month').annotate(
            total_amount=Sum('total_amount'),
            invoices_count=Count('id')
        ).order_by('month')

        # أفضل المنتجات (الأكثر مبيعاً)
        from invoices.models import InvoiceItem
        top_products = InvoiceItem.objects.filter(
            sales_invoice__invoice_date__range=[start_date, end_date],
            sales_invoice__status__in=['pending', 'partial', 'paid']
        ).values('description').annotate(
            total_quantity=Sum('quantity'),
            total_amount=Sum('total'),
            avg_price=Sum('total') / Sum('quantity')
        ).order_by('-total_amount')[:10]

        # أفضل العملاء (أعلى 10)
        top_customers = sales_by_customer[:10]

        # عدد العملاء الفريدين
        unique_customers_count = SalesInvoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['pending', 'partial', 'paid']
        ).values('customer').distinct().count()

        # متوسط قيمة الفاتورة
        average_invoice_value = (total_sales['total_amount'] / total_sales['count']) if total_sales['count'] > 0 else Decimal('0.00')

        # جميع الفواتير للعرض في التقرير
        invoices = SalesInvoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['pending', 'partial', 'paid']
        ).select_related('customer').order_by('-invoice_date')

        return {
            'total_sales': total_sales['total_amount'] or Decimal('0.00'),
            'total_tax': total_sales['total_tax'] or Decimal('0.00'),
            'invoices_count': total_sales['count'] or 0,
            'unique_customers': unique_customers_count,
            'average_invoice_value': average_invoice_value,
            'sales_by_customer': sales_by_customer,
            'top_customers': top_customers,
            'top_products': top_products,
            'sales_by_month': sales_by_month,
            'invoices': invoices,
            'start_date': start_date,
            'end_date': end_date
        }

    @staticmethod
    def get_purchase_summary(start_date, end_date):
        """تقرير ملخص المشتريات"""
        from invoices.models import PurchaseInvoice

        # إجمالي المشتريات
        total_purchases = PurchaseInvoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['pending', 'partial', 'paid']
        ).aggregate(
            total_amount=Sum('total_amount'),
            total_tax=Sum('tax_amount'),
            count=Count('id')
        )

        # المشتريات حسب الموردين
        purchases_by_supplier = PurchaseInvoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['pending', 'partial', 'paid']
        ).values('supplier__name').annotate(
            total_amount=Sum('total_amount'),
            invoices_count=Count('id')
        ).order_by('-total_amount')

        # المشتريات حسب الشهر
        purchases_by_month = PurchaseInvoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['pending', 'partial', 'paid']
        ).extra(
            select={'month': "strftime('%%Y-%%m', invoice_date)"}
        ).values('month').annotate(
            total_amount=Sum('total_amount'),
            invoices_count=Count('id')
        ).order_by('month')

        # أفضل المنتجات المشتراة
        from invoices.models import InvoiceItem
        top_purchased_items = InvoiceItem.objects.filter(
            purchase_invoice__invoice_date__range=[start_date, end_date],
            purchase_invoice__status__in=['pending', 'partial', 'paid']
        ).values('description').annotate(
            total_quantity=Sum('quantity'),
            total_amount=Sum('total'),
            avg_price=Sum('total') / Sum('quantity')
        ).order_by('-total_amount')[:10]

        # أفضل الموردين (أعلى 10)
        top_suppliers = purchases_by_supplier[:10]

        # عدد الموردين الفريدين
        unique_suppliers_count = PurchaseInvoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['pending', 'partial', 'paid']
        ).values('supplier').distinct().count()

        # متوسط قيمة الفاتورة
        average_invoice_value = (total_purchases['total_amount'] / total_purchases['count']) if total_purchases['count'] > 0 else Decimal('0.00')

        # جميع الفواتير للعرض في التقرير
        invoices = PurchaseInvoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['pending', 'partial', 'paid']
        ).select_related('supplier').order_by('-invoice_date')

        return {
            'total_purchases': total_purchases['total_amount'] or Decimal('0.00'),
            'total_tax': total_purchases['total_tax'] or Decimal('0.00'),
            'invoices_count': total_purchases['count'] or 0,
            'unique_suppliers': unique_suppliers_count,
            'average_invoice_value': average_invoice_value,
            'purchases_by_supplier': purchases_by_supplier,
            'top_suppliers': top_suppliers,
            'top_purchased_items': top_purchased_items,
            'purchases_by_month': purchases_by_month,
            'invoices': invoices,
            'start_date': start_date,
            'end_date': end_date
        }

    @staticmethod
    def get_tax_report(start_date, end_date):
        """تقرير الضرائب"""
        from invoices.models import SalesInvoice, PurchaseInvoice

        # ضرائب المبيعات
        sales_tax = SalesInvoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['pending', 'partial', 'paid']
        ).aggregate(
            total_tax=Sum('tax_amount'),
            total_before_tax=Sum('subtotal'),
            total_after_tax=Sum('total_amount')
        )

        # ضرائب المشتريات
        purchase_tax = PurchaseInvoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['pending', 'partial', 'paid']
        ).aggregate(
            total_tax=Sum('tax_amount'),
            total_before_tax=Sum('subtotal'),
            total_after_tax=Sum('total_amount')
        )

        # صافي الضريبة المستحقة
        net_tax = (sales_tax['total_tax'] or Decimal('0.00')) - (purchase_tax['total_tax'] or Decimal('0.00'))

        return {
            'sales_tax': sales_tax['total_tax'] or Decimal('0.00'),
            'sales_before_tax': sales_tax['total_before_tax'] or Decimal('0.00'),
            'sales_after_tax': sales_tax['total_after_tax'] or Decimal('0.00'),
            'purchase_tax': purchase_tax['total_tax'] or Decimal('0.00'),
            'purchase_before_tax': purchase_tax['total_before_tax'] or Decimal('0.00'),
            'purchase_after_tax': purchase_tax['total_after_tax'] or Decimal('0.00'),
            'net_tax_due': net_tax,
            'start_date': start_date,
            'end_date': end_date
        }

    @staticmethod
    def get_profit_loss_report(start_date, end_date):
        """تقرير الأرباح والخسائر"""
        # إجمالي المبيعات
        total_sales = SalesInvoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['pending', 'partial', 'paid']
        ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0.00')

        # إجمالي المشتريات
        total_purchases = PurchaseInvoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['pending', 'partial', 'paid']
        ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0.00')

        # إجمالي الربح/الخسارة
        gross_profit = total_sales - total_purchases

        return {
            'total_sales': total_sales,
            'total_purchases': total_purchases,
            'gross_profit': gross_profit,
            'profit_margin': (gross_profit / total_sales * 100) if total_sales > 0 else Decimal('0.00'),
            'start_date': start_date,
            'end_date': end_date
        }

    @staticmethod
    def get_balance_sheet_report(as_of_date=None):
        """تقرير الميزانية العمومية"""
        if not as_of_date:
            as_of_date = date.today()

        # الأصول - أرصدة العملاء
        customer_balances = ReportManager.get_customer_balances(as_of_date)
        total_receivables = sum(balance['balance'] for balance in customer_balances if balance['balance'] > 0)

        # الخصوم - أرصدة الموردين
        supplier_balances = ReportManager.get_supplier_balances(as_of_date)
        total_payables = sum(abs(balance['balance']) for balance in supplier_balances if balance['balance'] < 0)

        # النقدية (تقدير بناءً على المدفوعات)
        cash_received = Payment.objects.filter(
            payment_date__lte=as_of_date,
            payment_type='received'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        cash_paid = Payment.objects.filter(
            payment_date__lte=as_of_date,
            payment_type='paid'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        net_cash = cash_received - cash_paid

        # إجمالي الأصول
        total_assets = total_receivables + net_cash

        # حقوق الملكية (الأصول - الخصوم)
        equity = total_assets - total_payables

        return {
            'assets': {
                'cash': net_cash,
                'receivables': total_receivables,
                'total': total_assets
            },
            'liabilities': {
                'payables': total_payables,
                'total': total_payables
            },
            'equity': {
                'total': equity
            },
            'as_of_date': as_of_date
        }

    @staticmethod
    def get_overdue_invoices_report(as_of_date=None):
        """تقرير الفواتير المتأخرة"""
        if not as_of_date:
            as_of_date = date.today()

        # فواتير البيع المتأخرة
        overdue_sales = SalesInvoice.objects.filter(
            due_date__lt=as_of_date,
            status__in=['pending', 'partial']
        ).select_related('customer').order_by('due_date')

        # إضافة أيام التأخير لفواتير البيع
        for invoice in overdue_sales:
            invoice.days_overdue = (as_of_date - invoice.due_date).days

        # فواتير الشراء المتأخرة
        overdue_purchases = PurchaseInvoice.objects.filter(
            due_date__lt=as_of_date,
            status__in=['pending', 'partial']
        ).select_related('supplier').order_by('due_date')

        # إضافة أيام التأخير لفواتير الشراء
        for invoice in overdue_purchases:
            invoice.days_overdue = (as_of_date - invoice.due_date).days

        # حساب الإجماليات
        total_overdue_sales = overdue_sales.aggregate(
            total=Sum('total_amount')
        )['total'] or Decimal('0.00')

        total_overdue_purchases = overdue_purchases.aggregate(
            total=Sum('total_amount')
        )['total'] or Decimal('0.00')

        return {
            'overdue_sales': overdue_sales,
            'overdue_purchases': overdue_purchases,
            'total_overdue_sales': total_overdue_sales,
            'total_overdue_purchases': total_overdue_purchases,
            'total_overdue': total_overdue_sales + total_overdue_purchases,
            'as_of_date': as_of_date
        }

    @staticmethod
    def get_currency_report():
        """تقرير العملات وأسعار الصرف"""
        from core.models import Currency, ExchangeRateHistory

        currencies = Currency.objects.filter(is_active=True).order_by('name')
        base_currency = Currency.get_base_currency()

        currency_data = []
        for currency in currencies:
            # آخر تحديث لسعر الصرف
            last_rate_history = ExchangeRateHistory.objects.filter(
                currency=currency
            ).order_by('-date').first()

            currency_data.append({
                'currency': currency,
                'last_update': last_rate_history.date if last_rate_history else currency.rate_updated_at.date(),
                'previous_rate': last_rate_history.rate if last_rate_history else currency.exchange_rate,
                'rate_change': currency.exchange_rate - (last_rate_history.rate if last_rate_history else currency.exchange_rate)
            })

        return {
            'currencies': currency_data,
            'base_currency': base_currency,
            'total_currencies': currencies.count(),
            'active_currencies': currencies.filter(is_active=True).count()
        }

    @staticmethod
    def get_detailed_tax_report(start_date, end_date):
        """تقرير الضرائب المفصل"""
        from core.models import TaxType

        # الضرائب المطبقة في الفترة
        applied_taxes = TaxType.objects.filter(is_active=True)

        tax_details = []
        total_tax_collected = Decimal('0.00')
        total_tax_paid = Decimal('0.00')

        for tax_type in applied_taxes:
            # ضرائب المبيعات لهذا النوع
            sales_with_tax = SalesInvoice.objects.filter(
                invoice_date__range=[start_date, end_date],
                status__in=['pending', 'partial', 'paid']
            ).aggregate(
                total_tax=Sum('tax_amount'),
                total_before_tax=Sum('subtotal'),
                count=Count('id')
            )

            # ضرائب المشتريات لهذا النوع
            purchases_with_tax = PurchaseInvoice.objects.filter(
                invoice_date__range=[start_date, end_date],
                status__in=['pending', 'partial', 'paid']
            ).aggregate(
                total_tax=Sum('tax_amount'),
                total_before_tax=Sum('subtotal'),
                count=Count('id')
            )

            sales_tax = sales_with_tax['total_tax'] or Decimal('0.00')
            purchase_tax = purchases_with_tax['total_tax'] or Decimal('0.00')

            tax_details.append({
                'tax_type': tax_type,
                'sales_tax': sales_tax,
                'purchase_tax': purchase_tax,
                'net_tax': sales_tax - purchase_tax,
                'sales_count': sales_with_tax['count'] or 0,
                'purchase_count': purchases_with_tax['count'] or 0
            })

            total_tax_collected += sales_tax
            total_tax_paid += purchase_tax

        return {
            'tax_details': tax_details,
            'total_tax_collected': total_tax_collected,
            'total_tax_paid': total_tax_paid,
            'net_tax_due': total_tax_collected - total_tax_paid,
            'start_date': start_date,
            'end_date': end_date
        }

    @staticmethod
    def get_customer_balances(as_of_date):
        """تقرير أرصدة العملاء"""
        from accounts.models import Customer
        from decimal import Decimal

        customers = Customer.objects.all()
        customer_balances = []

        for customer in customers:
            # إجمالي المبيعات
            total_sales = SalesInvoice.objects.filter(
                customer=customer,
                invoice_date__lte=as_of_date,
                status__in=['pending', 'partial', 'paid']
            ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0.00')

            # إجمالي المدفوعات
            total_payments = Payment.objects.filter(
                customer=customer,
                payment_date__lte=as_of_date,
                payment_type='received'
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

            # الرصيد = المبيعات - المدفوعات
            balance = total_sales - total_payments

            customer_balances.append({
                'name': customer.name,
                'phone': customer.phone,
                'email': customer.email,
                'total_sales': total_sales,
                'total_payments': total_payments,
                'balance': balance
            })

        return customer_balances
