{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير المصروفات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-danger">
                    <i class="fas fa-receipt me-2"></i>تقرير المصروفات
                </h2>
                <div>
                    <a href="{% url 'reports:expenses_report_print' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" 
                       class="btn btn-danger" target="_blank">
                        <i class="fas fa-print me-2"></i>طباعة التقرير
                    </a>
                    <button class="btn btn-success" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>تصدير CSV
                    </button>
                    <a href="{% url 'reports:index' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                    </a>
                </div>
            </div>

            <!-- فلاتر التقرير -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="{{ start_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="{{ end_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>تطبيق الفلتر
                            </button>
                            <a href="{% url 'reports:expenses_report' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-1"></i>إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- الإحصائيات الملخصة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-white bg-danger">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ total_expenses|floatformat:2|default:0 }}</h4>
                                    <p class="card-text">إجمالي المصروفات (د.ع)</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ expenses_count|default:0 }}</h4>
                                    <p class="card-text">عدد المصروفات</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-list fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ average_expense|floatformat:2|default:0 }}</h4>
                                    <p class="card-text">متوسط المصروف (د.ع)</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-dark">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ max_expense|floatformat:2|default:0 }}</h4>
                                    <p class="card-text">أكبر مصروف (د.ع)</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-arrow-up fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المصروفات حسب الفئة -->
            {% if expenses_by_category %}
            <div class="card mb-4">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>المصروفات حسب الفئة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>فئة المصروف</th>
                                    <th>عدد المصروفات</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in expenses_by_category %}
                                <tr>
                                    <td>{{ category.category_display }}</td>
                                    <td>{{ category.count }}</td>
                                    <td class="text-end">{{ category.total|floatformat:2 }} د.ع</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar bg-danger" role="progressbar" 
                                                 style="width: {{ category.percentage }}%">
                                                {{ category.percentage|floatformat:1 }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- تفاصيل المصروفات -->
            {% if expenses %}
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>تفاصيل المصروفات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="expensesTable">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>رقم الفاتورة</th>
                                    <th>المورد</th>
                                    <th>الوصف</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for expense in expenses %}
                                <tr>
                                    <td>{{ expense.invoice_date|date:"d/m/Y" }}</td>
                                    <td>{{ expense.invoice_number }}</td>
                                    <td>{{ expense.supplier.name }}</td>
                                    <td>{{ expense.notes|default:"-" }}</td>
                                    <td class="text-end">{{ expense.total_amount|floatformat:2 }} د.ع</td>
                                    <td>
                                        {% if expense.status == 'paid' %}
                                            <span class="badge bg-success">مدفوعة</span>
                                        {% elif expense.status == 'pending' %}
                                            <span class="badge bg-warning">معلقة</span>
                                        {% elif expense.status == 'partial' %}
                                            <span class="badge bg-info">مدفوعة جزئياً</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                لا توجد مصروفات في الفترة المحددة.
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function exportReport() {
    // تصدير البيانات إلى CSV
    const table = document.getElementById('expensesTable');
    if (!table) return;
    
    let csv = 'التاريخ,رقم الفاتورة,المورد,الوصف,المبلغ,الحالة\n';
    
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        const rowData = Array.from(cells).map(cell => 
            '"' + cell.textContent.trim().replace(/"/g, '""') + '"'
        ).join(',');
        csv += rowData + '\n';
    });
    
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'تقرير_المصروفات_' + new Date().toISOString().split('T')[0] + '.csv';
    link.click();
}
</script>
{% endblock %}
