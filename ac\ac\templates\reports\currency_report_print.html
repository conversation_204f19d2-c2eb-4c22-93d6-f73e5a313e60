<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير العملات</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }
        
        .report-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
        }
        
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #ffc107;
        }
        
        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ffc107;
            margin-bottom: 10px;
        }
        
        .report-date {
            font-size: 1.2rem;
            color: #6c757d;
            margin: 0;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        
        .company-name {
            font-size: 1.5rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 5px;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-box {
            text-align: center;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ffc107;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #ffc107;
            margin: 30px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #ffc107;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .data-table th {
            background-color: #ffc107;
            color: #212529;
            font-weight: 600;
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #ffcd39;
            font-size: 0.9rem;
        }
        
        .data-table td {
            padding: 10px 8px;
            border: 1px solid #dee2e6;
            text-align: center;
            font-size: 0.85rem;
        }
        
        .data-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .data-table tfoot th {
            background-color: #e9ecef;
            color: #212529;
            font-weight: 700;
        }
        
        .text-end {
            text-align: left !important;
        }
        
        .text-right {
            text-align: right !important;
        }
        
        .currency-active {
            color: #198754;
            font-weight: 600;
        }
        
        .currency-inactive {
            color: #dc3545;
            font-weight: 600;
        }
        
        .base-currency {
            background-color: #fff3cd;
            font-weight: 700;
        }
        
        .exchange-rate {
            font-weight: 600;
            color: #0d6efd;
        }
        
        .footer-section {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 0;
                font-size: 12px;
            }
            
            .report-container {
                max-width: none;
                margin: 0;
                padding: 15mm;
            }
            
            .report-header {
                page-break-inside: avoid;
            }
            
            .summary-stats {
                page-break-inside: avoid;
            }
            
            .data-table {
                page-break-inside: auto;
            }
            
            .data-table tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
            
            .section-title {
                page-break-after: avoid;
            }
            
            .footer-section {
                page-break-inside: avoid;
            }
            
            @page {
                margin: 15mm;
                size: A4;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- رأس التقرير -->
        <div class="report-header">
            <h1 class="report-title">تقرير العملات</h1>
            <p class="report-date">
                تم الإنشاء في {{ current_date|date:"d/m/Y H:i" }}
            </p>
        </div>
        
        <!-- معلومات الشركة -->
        <div class="company-info">
            <h2 class="company-name">{{ company.company_name }}</h2>
            {% if company.company_name_en %}
                <p class="text-muted mb-1">{{ company.company_name_en }}</p>
            {% endif %}
            <p class="mb-1">{{ company.address }}</p>
            <p class="mb-0">هاتف: {{ company.phone }} | بريد إلكتروني: {{ company.email }}</p>
        </div>
        
        <!-- الإحصائيات الملخصة -->
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-label">إجمالي العملات</div>
                <div class="stat-value">{{ currencies.count|default:0 }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">العملات النشطة</div>
                <div class="stat-value">{{ currencies|length|default:0 }}</div>
            </div>
            <div class="stat-box">
                <div class="stat-label">العملة الأساسية</div>
                <div class="stat-value">د.ع</div>
            </div>
        </div>
        
        <!-- تفاصيل العملات -->
        <h2 class="section-title">تفاصيل العملات المدعومة</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 10%">الرمز</th>
                    <th style="width: 25%">اسم العملة</th>
                    <th style="width: 25%">الاسم بالإنجليزية</th>
                    <th style="width: 10%">الرمز</th>
                    <th style="width: 15%">سعر الصرف</th>
                    <th style="width: 10%">أساسية</th>
                    <th style="width: 10%">الحالة</th>
                </tr>
            </thead>
            <tbody>
                {% for currency in currencies %}
                <tr {% if currency.is_base_currency %}class="base-currency"{% endif %}>
                    <td class="text-center"><strong>{{ currency.code }}</strong></td>
                    <td class="text-right">{{ currency.name }}</td>
                    <td class="text-right">{{ currency.name_en|default:"-" }}</td>
                    <td class="text-center"><strong>{{ currency.symbol }}</strong></td>
                    <td class="text-end exchange-rate">
                        {% if currency.is_base_currency %}
                            1.000000
                        {% else %}
                            {{ currency.exchange_rate|floatformat:6 }}
                        {% endif %}
                    </td>
                    <td class="text-center">
                        {% if currency.is_base_currency %}
                            <span style="color: #198754;">✓ نعم</span>
                        {% else %}
                            <span style="color: #6c757d;">لا</span>
                        {% endif %}
                    </td>
                    <td class="text-center">
                        {% if currency.is_active %}
                            <span class="currency-active">نشطة</span>
                        {% else %}
                            <span class="currency-inactive">غير نشطة</span>
                        {% endif %}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" class="text-center">لا توجد عملات مسجلة في النظام</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <!-- إحصائيات استخدام العملات -->
        {% if currency_stats %}
        <h2 class="section-title">إحصائيات استخدام العملات</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 15%">رمز العملة</th>
                    <th style="width: 30%">اسم العملة</th>
                    <th style="width: 15%">فواتير البيع</th>
                    <th style="width: 15%">فواتير الشراء</th>
                    <th style="width: 15%">إجمالي المعاملات</th>
                    <th style="width: 10%">النسبة</th>
                </tr>
            </thead>
            <tbody>
                {% for stat in currency_stats %}
                <tr>
                    <td class="text-center"><strong>{{ stat.currency.code }}</strong></td>
                    <td class="text-right">{{ stat.currency.name }}</td>
                    <td class="text-center">{{ stat.sales_count|default:0 }}</td>
                    <td class="text-center">{{ stat.purchase_count|default:0 }}</td>
                    <td class="text-center">{{ stat.total_transactions|default:0 }}</td>
                    <td class="text-center">
                        {% if stat.total_transactions > 0 %}
                            {% widthratio stat.total_transactions 100 100 %}%
                        {% else %}
                            0%
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endif %}
        
        <!-- معلومات أسعار الصرف -->
        <div style="margin-bottom: 30px;">
            <h3 class="section-title">معلومات أسعار الصرف</h3>
            <div style="padding: 15px; background-color: #fff3cd; border-radius: 8px; border-right: 4px solid #ffc107;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h5 style="color: #856404; margin-bottom: 10px;">العملة الأساسية:</h5>
                        <p style="margin: 0; font-weight: 600;">الدينار العراقي (د.ع)</p>
                        <p style="margin: 0; font-size: 0.9rem; color: #6c757d;">سعر الصرف: 1.000000</p>
                    </div>
                    <div>
                        <h5 style="color: #856404; margin-bottom: 10px;">آخر تحديث:</h5>
                        <p style="margin: 0; font-weight: 600;">{{ current_date|date:"d/m/Y H:i" }}</p>
                        <p style="margin: 0; font-size: 0.9rem; color: #6c757d;">تحديث تلقائي</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- ملاحظات -->
        <div style="margin-bottom: 30px;">
            <h3 class="section-title">ملاحظات</h3>
            <div style="padding: 15px; background-color: #f8f9fa; border-radius: 8px; border-right: 4px solid #ffc107;">
                <ul style="margin: 0; padding-right: 20px;">
                    <li><strong>العملة الأساسية:</strong> الدينار العراقي هو العملة الأساسية للنظام</li>
                    <li><strong>سعر الصرف:</strong> يتم حساب جميع أسعار الصرف مقابل الدينار العراقي</li>
                    <li><strong>العملات النشطة:</strong> فقط العملات النشطة تظهر في قوائم الاختيار</li>
                    <li><strong>التحديث:</strong> يتم تحديث أسعار الصرف بشكل دوري</li>
                </ul>
            </div>
        </div>
        
        <!-- تذييل التقرير -->
        <div class="footer-section">
            <p class="mb-1">تم إنشاء هذا التقرير في {{ current_date|date:"d/m/Y H:i" }}</p>
            <p class="mb-0">{{ company.company_name }} - نظام الفواتير العراقي</p>
        </div>
    </div>
    
    <!-- أزرار الطباعة -->
    <div class="no-print" style="text-align: center; margin: 30px 0;">
        <button onclick="window.print()" style="background: #ffc107; color: #212529; border: none; padding: 12px 24px; border-radius: 5px; font-size: 16px; margin: 0 10px; cursor: pointer;">
            طباعة التقرير
        </button>
        <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 5px; font-size: 16px; margin: 0 10px; cursor: pointer;">
            إغلاق
        </button>
    </div>
</body>
</html>
